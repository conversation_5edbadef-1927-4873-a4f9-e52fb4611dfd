<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.hzero.message.infra.mapper.EmailRuleConfigMapper">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap id="BaseResultMap" type="org.hzero.message.domain.entity.EmailRuleConfig">
        <result column="rule_config_id" property="ruleConfigId" jdbcType="VARCHAR"/>
        <result column="scene_id" property="sceneId" jdbcType="VARCHAR"/>
        <result column="group_id" property="groupId" jdbcType="DECIMAL"/>
        <result column="group_name" property="groupName" jdbcType="VARCHAR"/>
        <result column="group_en_name" property="groupEnName" jdbcType="VARCHAR"/>
        <result column="language" property="language" jdbcType="VARCHAR"/>
        <result column="send_mail_box" property="sendMailBox" jdbcType="VARCHAR"/>
        <result column="send_mail_box_name" property="sendMailBoxName" jdbcType="VARCHAR"/>
        <result column="template_id" property="templateId" jdbcType="DECIMAL"/>
        <result column="template_code" property="templateCode" jdbcType="VARCHAR"/>
        <result column="template_name" property="templateName" jdbcType="VARCHAR"/>
        <result column="sign_name" property="signName" jdbcType="VARCHAR"/>
        <result column="object_version_number" property="objectVersionNumber" jdbcType="DECIMAL"/>
        <result column="creation_date" property="creationDate" jdbcType="DATE"/>
        <result column="created_by" property="createdBy" jdbcType="DECIMAL"/>
        <result column="last_updated_by" property="lastUpdatedBy" jdbcType="DECIMAL"/>
        <result column="last_update_date" property="lastUpdateDate" jdbcType="DATE"/>
        <result column="deleted" property="deleted" jdbcType="BIT"/>
    </resultMap>

    <select id="queryEmailRuleConfigListPage" parameterType="org.hzero.message.api.dto.EmailRuleConfigQueryDTO"
            resultType="org.hzero.message.domain.vo.EmailRuleConfigResVO">
        SELECT
        t.rule_config_id,
        t.scene_id,
        t.group_id,
        t.group_name,
        t.group_en_name,
        t.language,
        t.send_mail_box,
        t.send_mail_box_name,
        t.template_id,
        t.template_code,
        t.template_name,
        t.sign_name,
        t.creation_date,
        t.created_by,
        t.last_update_date,
        t.last_updated_by,
        t.deleted
        FROM hmsg_email_rule_config t
        <where>
            t.deleted = 0
            <if test="sceneId != null and sceneId != ''">
                <bind name="sceneIdLike" value="'%'+sceneId+'%'"/>
                AND t.scene_id LIKE #{sceneIdLike}
            </if>
            <if test="templateCode != null and templateCode != ''">
                <bind name="templateCodeLike" value="'%'+templateCode+'%'"/>
                AND t.template_code LIKE #{templateCodeLike}
            </if>
            <if test="groupId != null ">
                AND t.group_id = #{groupId}
            </if>
            <if test="language != null and language != ''">
                AND t.language = #{language}
            </if>
            <if test="sendMailBox != null and sendMailBox != ''">
                AND t.send_mail_box = #{sendMailBox}
            </if>
        </where>
    </select>

    <select id="export" parameterType="org.hzero.message.api.dto.EmailRuleConfigQueryDTO"
            resultType="org.hzero.message.api.dto.EmailRuleConfigExportDTO">
        SELECT
        t.scene_id as sceneId,
        t.group_id as groupId,
        (case when #{lang} = 'zh_CN' then t.group_name else t.group_en_name end) AS groupName,
        t.language,
        t.send_mail_box as sendMailBox,
        t.send_mail_box_name as sendMailBoxName,
        t.template_code as templateCode,
        t.template_name as templateName,
        t.sign_name as signName
        FROM hmsg_email_rule_config t
        <where>
            t.deleted = 0
            <if test="sceneId != null and sceneId != ''">
                <bind name="sceneIdLike" value="'%'+sceneId+'%'"/>
                AND t.scene_id LIKE #{sceneIdLike}
            </if>
            <if test="templateCode != null and templateCode != ''">
                <bind name="templateCodeLike" value="'%'+templateCode+'%'"/>
                AND t.template_code LIKE #{templateCodeLike}
            </if>
            <if test="groupId != null ">
                AND t.group_id = #{groupId}
            </if>
            <if test="language != null and language != ''">
                AND t.language = #{language}
            </if>
            <if test="sendMailBox != null and sendMailBox != ''">
                AND t.send_mail_box = #{sendMailBox}
            </if>
        </where>
        order by t.last_update_date desc
    </select>

    <select id="queryEmailRuleConfigListImport" parameterType="org.hzero.message.api.dto.EmailRuleConfigInputDTO"
            resultType="org.hzero.message.domain.vo.EmailRuleConfigResImportVO">
        SELECT
        t.rule_config_id,
        t.scene_id,
        t.group_id,
        t.group_name,
        t.group_en_name,
        t.language,
        t.send_mail_box,
        t.send_mail_box_name,
        t.template_id,
        t.template_code,
        t.template_name,
        t.sign_name,
        t.object_version_number
        FROM hmsg_email_rule_config t
        <where>
            t.deleted = 0
            <if test="list != null and list.size() > 0">
                and
                <foreach collection="list" item="item" index="index" open="(" separator="or" close=")">
                    t.scene_id = #{item.sceneId} and t.group_id = #{item.groupId} and t.language = #{item.language}
                </foreach>
            </if>
        </where>
    </select>

    <insert id="batchInsertByNative">
        INSERT INTO hmsg_email_rule_config
        (rule_config_id, scene_id, group_id, group_name, group_en_name, language, send_mail_box, send_mail_box_name,
        template_id, template_code, template_name, sign_name, object_version_number, creation_date, created_by,
        last_updated_by, last_update_date, deleted) VALUES
        <foreach collection="list" separator="," item="item">
            (#{item.ruleConfigId},#{item.sceneId},#{item.groupId},#{item.groupName},#{item.groupEnName},#{item.language},
            #{item.sendMailBox},#{item.sendMailBoxName},#{item.templateId},#{item.templateCode},#{item.templateName},
            #{item.signName},#{item.objectVersionNumber},#{item.creationDate},#{item.createdBy},#{item.lastUpdatedBy},
            #{item.lastUpdateDate},#{item.deleted})
        </foreach>
    </insert>

    <update id="batchUpdateByNative" parameterType="java.util.List">
        update hmsg_email_rule_config
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="scene_id = case" suffix="end,">
                <foreach item="item" collection="list">
                    when rule_config_id = (#{item.ruleConfigId})
                    then #{item.sceneId}
                </foreach>
            </trim>
            <trim prefix="group_id = case" suffix="end,">
                <foreach item="item" collection="list">
                    when rule_config_id = (#{item.ruleConfigId})
                    then #{item.groupId}
                </foreach>
            </trim>
            <trim prefix="group_name = case" suffix="end,">
                <foreach item="item" collection="list">
                    when rule_config_id = (#{item.ruleConfigId})
                    then #{item.groupName}
                </foreach>
            </trim>
            <trim prefix="group_en_name = case" suffix="end,">
                <foreach item="item" collection="list">
                    when rule_config_id = (#{item.ruleConfigId})
                    then #{item.groupEnName}
                </foreach>
            </trim>
            <trim prefix="language = case" suffix="end,">
                <foreach item="item" collection="list">
                    when rule_config_id = (#{item.ruleConfigId})
                    then #{item.language}
                </foreach>
            </trim>
            <trim prefix="send_mail_box = case" suffix="end,">
                <foreach item="item" collection="list">
                    when rule_config_id = (#{item.ruleConfigId})
                    then #{item.sendMailBox}
                </foreach>
            </trim>
            <trim prefix="send_mail_box_name = case" suffix="end,">
                <foreach item="item" collection="list">
                    when rule_config_id = (#{item.ruleConfigId})
                    then #{item.sendMailBoxName}
                </foreach>
            </trim>
            <trim prefix="template_id = case" suffix="end,">
                <foreach item="item" collection="list">
                    when rule_config_id = (#{item.ruleConfigId})
                    then #{item.templateId}
                </foreach>
            </trim>
            <trim prefix="template_code = case" suffix="end,">
                <foreach item="item" collection="list">
                    when rule_config_id = (#{item.ruleConfigId})
                    then #{item.templateCode}
                </foreach>
            </trim>
            <trim prefix="template_name = case" suffix="end,">
                <foreach item="item" collection="list">
                    when rule_config_id = (#{item.ruleConfigId})
                    then #{item.templateName}
                </foreach>
            </trim>
            <trim prefix="sign_name = case" suffix="end,">
                <foreach item="item" collection="list">
                    when rule_config_id = (#{item.ruleConfigId})
                    then #{item.signName}
                </foreach>
            </trim>
            <trim prefix="last_updated_by = case" suffix="end,">
                <foreach item="item" collection="list">
                    when rule_config_id = (#{item.ruleConfigId})
                    then #{item.lastUpdatedBy}
                </foreach>
            </trim>
            <trim prefix="last_update_date = case" suffix="end,">
                <foreach item="item" collection="list">
                    when rule_config_id = (#{item.ruleConfigId})
                    then #{item.lastUpdateDate}
                </foreach>
            </trim>
            <trim prefix="object_version_number = case" suffix="end,">
                <foreach item="item" collection="list">
                    when rule_config_id = (#{item.ruleConfigId})
                    then #{item.objectVersionNumber}
                </foreach>
            </trim>

        </trim>
        where deleted = 0 and
        <foreach item="item" collection="list" separator="or">
            (rule_config_id = (#{item.ruleConfigId}))
        </foreach>

    </update>

    <select id="queryEmailRuleConfigCmList" resultType="org.hzero.message.domain.vo.EmailConfigTemplateInfoVO">
        SELECT
        t.rule_config_id,
        t.scene_id,
        t.group_id,
        t.language,
        t.send_mail_box,
        t.send_mail_box_name,
        t.template_id,
        t.template_code,
        t.template_name,
        t.sign_name,
        m.template_title,
        m.template_content,
        s.sender
        FROM hmsg_email_rule_config t
        INNER JOIN hmsg_message_template m ON t.template_id = m.template_id
        INNER JOIN hmsg_email_server s ON t.send_mail_box = s.server_code and s.enabled_flag = 1
        <where>
            t.deleted = 0
            <if test="list != null and list.size() > 0">
                and
                <foreach collection="list" item="item" index="index" open="(" separator="or" close=")">
                    t.scene_id = #{item.sceneId} and t.group_id = #{item.groupId} and t.language = #{item.language}
                </foreach>
            </if>
        </where>
    </select>

</mapper>