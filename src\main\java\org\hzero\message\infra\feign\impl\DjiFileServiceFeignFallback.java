package org.hzero.message.infra.feign.impl;


import feign.hystrix.*;
import lombok.extern.slf4j.*;
import org.hzero.message.domain.entity.*;
import org.hzero.message.infra.feign.*;
import org.springframework.http.*;
import org.springframework.stereotype.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class DjiFileServiceFeignFallback implements FallbackFactory<DjiFileServiceFeign> {
    @Override
    public DjiFileServiceFeign create(Throwable throwable) {
        return new DjiFileServiceFeign() {

            @Override
            public ResponseEntity<DjiFile> file2Id(Long organizationId, String bucketName, String directory,
                                                   String fileName, String fileType, String storageCode, byte[] byteFile) {
                log.error("current DjiFileFeign create has error", throwable);
                return null;
            }

        };
    }
}
