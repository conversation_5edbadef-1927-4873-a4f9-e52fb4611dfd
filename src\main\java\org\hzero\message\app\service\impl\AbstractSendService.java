package org.hzero.message.app.service.impl;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.hzero.boot.message.entity.MessageSender;
import org.hzero.core.base.BaseConstants.Flag;
import org.hzero.message.api.dto.UserMessageDTO;
import org.hzero.message.app.service.MessageReceiverService;
import org.hzero.message.domain.entity.Message;
import org.hzero.message.domain.entity.MessageTransaction;
import org.hzero.message.domain.repository.MessageRepository;
import org.hzero.message.domain.repository.MessageTransactionRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * @Author: hp
 * @Date: 2020/2/12 21:47
 */
public abstract class AbstractSendService {

    private Logger log = LoggerFactory.getLogger(WebSendServiceImpl.class);

    @Autowired
    private MessageRepository messageRepository;
    @Autowired
    private MessageTransactionRepository messageTransactionRepository;

    @Autowired
    private MessageReceiverService messageReceiverService;

    public AbstractSendService() {
    }


    Message createMessage(MessageSender messageSender, String messageTypeCode) {
        Message message = (new Message()).setSendFlag(Flag.NO).setTenantId(messageSender.getTenantId()).setTemplateCode(messageSender.getMessageCode()).setLang(messageSender.getLang()).setMessageTypeCode(messageTypeCode).setServerCode(messageSender.getServerCode());
        this.messageRepository.insertSelective(message);
        return message;
    }

    void failedProcess(Message message, Exception e) {
        this.messageRepository.updateOptional(message.setSendFlag(Flag.NO), new String[]{"sendFlag"});
        this.messageTransactionRepository.insertSelective((new MessageTransaction()).setMessageId(message.getMessageId()).setTrxStatusCode("F").setTenantId(message.getTenantId()).setTransactionMessage(ExceptionUtils.getStackTrace(e)));
    }

    void successProcessUpdate(UserMessageDTO message) {
        MessageTransaction messageTransaction = (new MessageTransaction()).setTransactionId(message.getTransactionId()).setTrxStatusCode("S");
        messageTransaction.setObjectVersionNumber(message.getTransactionObjectVersionNumber());
        this.messageTransactionRepository.updateOptional(messageTransaction, new String[]{"trxStatusCode", "transactionMessage"});
    }

    void failedProcessUpdate(UserMessageDTO message, Exception e) {
        this.messageRepository.updateOptional(message.setSendFlag(Flag.NO), new String[]{"sendFlag"});
        MessageTransaction messageTransaction = (new MessageTransaction()).setTransactionId(message.getTransactionId()).setTrxStatusCode("F").setTransactionMessage(ExceptionUtils.getStackTrace(e));
        messageTransaction.setObjectVersionNumber(message.getTransactionObjectVersionNumber());
        this.messageTransactionRepository.updateOptional(messageTransaction, new String[]{"trxStatusCode", "transactionMessage"});
    }

}
