package org.hzero.message.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.service.Tag;
import springfox.documentation.spring.web.plugins.Docket;

/**
 * 消息服务swagger注解
 *
 * <AUTHOR>
 */
@Configuration
public class DjiMessageSwaggerApiConfig {

    public static final String SEND_MESSAGE= "SendMessage";
    public static final String SEND_CALL= "SendCall";
    public static final String SEND_GT= "SendGtMessage";
    public static final String EMAIL_RULE_CONFIG= "EmailRuleConfig";


    @Autowired
    public DjiMessageSwaggerApiConfig(Docket docket) {
        docket.tags(new Tag(SEND_MESSAGE, "消息发送相关接口"));
        docket.tags(new Tag(SEND_CALL, "电话告警相关接口"));
        docket.tags(new Tag(SEND_GT, "GT消息发送相关接口"));
        docket.tags(new Tag(EMAIL_RULE_CONFIG, "邮件规则配置表相关接口"));
    }
}

