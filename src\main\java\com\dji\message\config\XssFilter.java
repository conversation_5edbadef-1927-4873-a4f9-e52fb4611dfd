package com.dji.message.config;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.hzero.message.api.dto.NoticeDTO;
import org.hzero.message.domain.entity.NoticeContent;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.safety.Cleaner;
import org.jsoup.safety.Whitelist;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;

/**
 * <p>
 * Cloud bus过滤器，用于 Cloud bus鉴权
 * </p>
 *
 * <AUTHOR>  2020/10/15 19:33
 */

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.Charset;

@SuppressWarnings("all")
@WebFilter(filterName = "XssFilter", urlPatterns = "v1/oauth/test")
public class XssFilter implements Filter {
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        if (request instanceof HttpServletRequest) {
            HttpServletRequest httpServletRequest = (HttpServletRequest) request;

            // 读取POST请求内容
            BufferedReader bufferedReader = httpServletRequest.getReader();
            StringBuilder requestBody = new StringBuilder();
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                requestBody.append(line);
            }

            // 修改POST请求内容
            String modifiedContent = modifyRequestContent(requestBody.toString());

            // 创建包装原始请求的HttpServletRequestWrapper
            HtmlEntityEncodingRequestWrapper wrappedRequest = new HtmlEntityEncodingRequestWrapper(httpServletRequest, modifiedContent);

            chain.doFilter(wrappedRequest, response);
        } else {
            chain.doFilter(request, response);
        }
    }

    private String modifyRequestContent(String originalContent) {
        // 读取请求的body
        try {
            if(StringUtils.isNotBlank(originalContent)){
                //String转成对应的NoticeDTO对象
                NoticeDTO noticeDTO=JSON.parseObject(originalContent, NoticeDTO.class);
                NoticeContent noticeContent=noticeDTO.getNoticeContent();
                String noticeContentStr=noticeContent.getNoticeBody();
                String noticeContentSecondBody=noticeContent.getSecondBody();
                // 建立白名单
                Whitelist whitelist = Whitelist.basic();
                //添加允许的标签属性
                whitelist.addAttributes("p", "style");
                whitelist.addAttributes("span", "style");
                // 这个方法用于对原始内容进行修改
                // 在这里可以根据需要进行定制的内容修改
                String result=Jsoup.clean(noticeContentStr, whitelist);
                String secondBodyResult=Jsoup.clean(noticeContentSecondBody, whitelist);
                noticeContent.setNoticeBody(result);
                noticeContent.setSecondBody(secondBodyResult);

                ObjectMapper objectMapper = new ObjectMapper();
                return objectMapper.writeValueAsString(noticeDTO);
            }
            return originalContent;
        } catch (Exception e) {
            return originalContent;
        }
    }

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @Override
    public void destroy() {
    }

}




