ARG IMAGE_HOST

FROM ${IMAGE_HOST}/public-service-group/ubuntu-jdk8-mysql-redis:latest

ARG JAR_FILE
ADD target/${JAR_FILE} /usr/share/dava/application.jar
ADD bin/enterpoint.sh /usr/share/dava/enterpoint.sh
ADD bin/apollo.properties /usr/share/dava/apollo.properties

# 设置为东8区(/usr/share/zoneinfo/Asia/Shanghai)
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo 'Asia/Shanghai' >/etc/timezone
RUN chmod a+x /usr/share/dava/enterpoint.sh && chmod a+x /usr/share/dava/application.jar
WORKDIR /usr/share/dava/
ENTRYPOINT ["/usr/share/dava/enterpoint.sh"]
