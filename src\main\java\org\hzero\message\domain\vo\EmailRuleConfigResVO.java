

package org.hzero.message.domain.vo;

import io.choerodon.mybatis.domain.*;
import io.swagger.annotations.*;
import lombok.*;
import lombok.experimental.*;
import org.hzero.core.cache.*;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("邮件规则配置返回vo")
public class EmailRuleConfigResVO extends AuditDomain implements Cacheable {

    @ApiModelProperty(value = "id")
    private String ruleConfigId;

    @ApiModelProperty(value = "场景id")
    private String sceneId;

    @ApiModelProperty(value = "组别id")
    private Long groupId;

    @ApiModelProperty(value = "组别名称")
    private String groupName;

    @ApiModelProperty(value = "组别英文名称")
    private String groupEnName;

    @ApiModelProperty(value = "语种")
    private String language;

    @ApiModelProperty(value = "发件邮箱")
    private String sendMailBox;

    @ApiModelProperty(value = "发件邮箱名称")
    private String sendMailBoxName;

    @ApiModelProperty("消息模板ID")
    private Long templateId;

    @ApiModelProperty(value = "消息模板编码")
    private String templateCode;

    @ApiModelProperty(value = "消息模板名称")
    private String templateName;

    @ApiModelProperty(value = "发件落款")
    private String signName;

    @ApiModelProperty(value = "是否删除")
    private Boolean deleted;

    @CacheValue(key = "hiam:user", primaryKey = "createdBy", searchKey = "realName", db = 1,
            structure = CacheValue.DataStructure.MAP_OBJECT)
    @ApiModelProperty(value = "创建用户名称")
    private String createdUserName;

    @CacheValue(key = "hiam:user", primaryKey = "lastUpdatedBy", searchKey = "realName", db = 1,
            structure = CacheValue.DataStructure.MAP_OBJECT)
    @ApiModelProperty(value = "'更新用户名称")
    private String lastUpdatedByName;

    @ApiModelProperty(value = "'邮箱发送人")
    private String sender;

}
