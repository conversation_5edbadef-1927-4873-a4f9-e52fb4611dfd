package org.hzero.message.imported;

import com.alibaba.fastjson.*;
import com.ctrip.framework.apollo.*;
import com.fasterxml.jackson.databind.*;
import lombok.*;
import lombok.extern.slf4j.*;
import org.apache.commons.collections4.*;
import org.apache.commons.lang3.*;
import org.hzero.boot.imported.app.service.*;
import org.hzero.boot.imported.domain.entity.*;
import org.hzero.boot.imported.infra.validator.annotation.*;
import org.hzero.boot.platform.lov.adapter.*;
import org.hzero.boot.platform.lov.dto.*;
import org.hzero.core.base.*;
import org.hzero.core.helper.*;
import org.hzero.core.message.*;
import org.hzero.message.api.dto.*;
import org.hzero.message.app.service.*;
import org.hzero.message.constant.*;
import org.hzero.message.domain.entity.*;
import org.hzero.message.domain.repository.*;
import org.hzero.message.domain.vo.*;
import org.hzero.message.infra.constant.*;
import org.hzero.message.util.*;
import org.hzero.mybatis.domian.*;
import org.hzero.mybatis.util.*;

import java.util.*;
import java.util.concurrent.*;
import java.util.function.*;
import java.util.stream.*;

/**
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
@ImportValidators({@ImportValidator(templateCode = TemplateConstants.ImportTemplateCode.HMSG_EMAIL_RULE_CONFIG)})
public class EmailRuleConfigInputImportValidator extends BatchValidatorHandler {

    private static final String DJICM_BRAND = "DJICM_BRAND";
    private static final String IMPORT_LIMIT = "IMPORT_LIMIT";
    private static final String REGEX = "\\$\\{.*?\\}";

    private final ObjectMapper objectMapper;

    private final LovAdapter lovAdapter;

    private final EmailRuleConfigService emailRuleConfigService;

    private final EmailServerRepository emailServerRepository;

    private final MessageTemplateRepository messageTemplateRepository;

    @SuppressWarnings("java:S3776")
    @Override
    public boolean validate(List<ImportData> dataList) {
        List<ValidatorPair<EmailRuleConfigInputDTO>> validatorPairs = DjiImportUtils.convertToValidatorPairs(objectMapper, EmailRuleConfigInputDTO.class, dataList);
        if (CollectionUtils.isEmpty(validatorPairs)) {
            log.info("EmailRuleConfigInputImportValidator无导入数据");
            return false;
        }
        log.info("导入数量:{}", validatorPairs.size());
        Integer importLimit = ConfigService.getAppConfig().getIntProperty(IMPORT_LIMIT, 5000);
        if (validatorPairs.size() > importLimit) {
            log.info("导入数量超过限制:{}", validatorPairs.size());
            validatorPairs.forEach(pair -> DjiImportUtils.appendErrorMsg(pair,
                    MessageAccessor.getMessage(ErrorCodeConstant.ERROR_IMPORT_DATA_EXCEEDS_UPPER_LIMIT, LanguageHelper.locale()).desc()));
            return false;
        }
        // 唯一性校验
        uniqueUnionCheck(validatorPairs);
        CompletableFuture<List<LovValueDTO>> lovValueListFuture = CompletableFuture.supplyAsync(() -> lovAdapter.queryLovValue(DJICM_BRAND, 0L));
        // 对应组别id是否有效
        List<OrderCustomerGroupVO> customerGroupList = emailRuleConfigService.queryCustomerGroupList();
        Map<Integer, OrderCustomerGroupVO> groupIdToCustomerGroupMap = customerGroupList.stream()
                .collect(Collectors.toMap(OrderCustomerGroupVO::getId, Function.identity(), (v1, v2) -> v1));
        // 发件邮箱状态=启用的记录
        List<String> sendMailBoxList = validatorPairs.stream()
                .map(validatorPair -> validatorPair.target().getSendMailBox()).collect(Collectors.toList());
        List<EmailServer> emailServerList = emailServerRepository.selectByCondition(Condition.builder(EmailServer.class).andWhere(Sqls.custom()
                .andIn(EmailServer.FIELD_SERVER_CODE, sendMailBoxList)
                .andEqualTo(EmailServer.FIELD_ENABLED_FLAG, 1)).build());
        Map<String, EmailServer> emailServerMap = emailServerList.stream()
                .collect(Collectors.toMap(EmailServer::getServerCode, Function.identity(), (v1, v2) -> v1));
        // 消息模板中查询是否存在状态=启用的记录
        List<String> templateCodeList = validatorPairs.stream()
                .map(validatorPair -> validatorPair.target().getTemplateCode()).collect(Collectors.toList());
        List<MessageTemplate> messageTemplateList = messageTemplateRepository.selectByCondition(Condition.builder(MessageTemplate.class).andWhere(Sqls.custom()
                .andIn(MessageTemplate.FIELD_TEMPLATE_CODE, templateCodeList)
                .andEqualTo(MessageTemplate.FIELD_ENABLED_FLAG, 1)).build());
        Map<String, MessageTemplate> messageTemplateMap = messageTemplateList.stream()
                .collect(Collectors.toMap(MessageTemplate::getTemplateCode, Function.identity(), (v1, v2) -> v1));
        //单行数据校验
        List<LovValueDTO> lovValueList = lovValueListFuture.join();
        validatorPairs.forEach(validate -> baseCheck(validate, lovValueList, groupIdToCustomerGroupMap, emailServerMap, messageTemplateMap));
        return Boolean.TRUE;
    }

    private void baseCheck(ValidatorPair<EmailRuleConfigInputDTO> validatorPair,
                           List<LovValueDTO> lovValueList,
                           Map<Integer, OrderCustomerGroupVO> groupIdToCustomerGroupMap,
                           Map<String, EmailServer> emailServerMap,
                           Map<String, MessageTemplate> messageTemplateMap) {
        log.info("EmailRuleConfigInputImportValidator校验参数:{}", JSON.toJSONString(validatorPair.target()));
        StringBuilder errorMsg = new StringBuilder();
        // 组别
        OrderCustomerGroupVO customerGroup = groupIdToCustomerGroupMap.get(validatorPair.target().getGroupId().intValue());
        if (Objects.isNull(customerGroup)) {
            errorMsg.append(MessageAccessor.getMessage(ErrorCodeConstant.ERROR_REQUIREMENT_PLAN_BUSINESS_GROUP_NOT_FOUND, LanguageHelper.locale()).desc());
        }
        // 发件邮箱
        if (Objects.isNull(emailServerMap.get(validatorPair.target().getSendMailBox()))) {
            errorMsg.append(MessageAccessor.getMessage(ErrorCodeConstant.ERROR_INVALID_SEND_EMAIL, LanguageHelper.locale()).desc());
        }
        // 消息模板
        if (Objects.isNull(messageTemplateMap.get(validatorPair.target().getTemplateCode()))) {
            errorMsg.append(MessageAccessor.getMessage(ErrorCodeConstant.ERROR_INVALID_MESSAGE_TEMPLATE, LanguageHelper.locale()).desc());
        }
        // 品牌隔离风险检查
        String signName = validatorPair.target().getSignName();
        MessageTemplate messageTemplate = messageTemplateMap.get(validatorPair.target().getTemplateCode());
        String templateContent = Objects.nonNull(messageTemplate) && StringUtils.isNotBlank(messageTemplate.getTemplateContent()) ? messageTemplate.getTemplateContent() : "";
        templateContent = templateContent.replaceAll(REGEX, "");
        String brand = Objects.nonNull(customerGroup) ? customerGroup.getBrand() : "";
        List<String> descriptionList = lovValueList.stream().filter(item -> !item.getValue().equalsIgnoreCase(brand))
                .map(LovValueDTO::getDescription).collect(Collectors.toList());
        List<String> keywords = new ArrayList<>();
        descriptionList.stream().filter(StringUtils::isNotBlank).forEach(description ->
                keywords.addAll(Arrays.stream(description.split(BaseConstants.Symbol.SEMICOLON)).collect(Collectors.toList())));
        for (String keyword : keywords) {
            if (StringUtils.isNotBlank(signName) && signName.toLowerCase().contains(keyword.toLowerCase())) {
                errorMsg.append(MessageAccessor.getMessage(ErrorCodeConstant.ERROR_THE_CONTENT_DOES_NOT_MATCH_THE_BRAND, LanguageHelper.locale()).desc());
            }
            if (templateContent.toLowerCase().contains(keyword.toLowerCase())) {
                errorMsg.append(MessageAccessor.getMessage(ErrorCodeConstant.ERROR_THE_CONTENT_DOES_NOT_MATCH_THE_BRAND, LanguageHelper.locale()).desc());
            }
            if (Objects.nonNull(messageTemplate) && StringUtils.isNotBlank(messageTemplate.getTemplateTitle()) && messageTemplate.getTemplateTitle().toLowerCase().contains(keyword.toLowerCase())) {
                errorMsg.append(MessageAccessor.getMessage(ErrorCodeConstant.ERROR_THE_CONTENT_DOES_NOT_MATCH_THE_BRAND, LanguageHelper.locale()).desc());
            }
        }
        if (StringUtils.isNotBlank(errorMsg.toString())) {
            DjiImportUtils.appendErrorMsg(validatorPair, errorMsg.toString());
        }
    }

    private void uniqueUnionCheck(List<ValidatorPair<EmailRuleConfigInputDTO>> validatorPairs) {
        // 校验本次导入数据是否重复 场景id+组别id+语种
        Map<String, List<ValidatorPair<EmailRuleConfigInputDTO>>> emailRuleConfigImportMap = validatorPairs.stream().collect(Collectors.groupingBy(validatorPair ->
                validatorPair.target().getSceneId().toUpperCase().trim().concat("-")
                        .concat(String.valueOf(validatorPair.target().getGroupId())).trim().concat("-")
                        .concat(validatorPair.target().getLanguage().trim())));
        //判断不为空,不然会空指针
        if (!emailRuleConfigImportMap.isEmpty()) {
            emailRuleConfigImportMap.forEach((key, values) -> {
                if (values.size() > BaseConstants.Digital.ONE) {
                    values.forEach(value -> DjiImportUtils.appendErrorMsg(value, MessageAccessor
                            .getMessage(ErrorCodeConstant.IMPORT_DATA_REPEAT, LanguageHelper.locale()).desc()));
                }
            });
        }
    }
}
