

package org.hzero.message.domain.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.choerodon.core.oauth.CustomUserDetails;
import io.choerodon.core.oauth.DetailsHelper;
import io.choerodon.mybatis.annotation.ModifyAudit;
import io.choerodon.mybatis.annotation.VersionAudit;
import io.choerodon.mybatis.domain.AuditDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import org.apache.commons.lang3.time.FastDateFormat;
import org.hzero.core.convert.CommonConverter;
import org.hzero.core.redis.RedisHelper;
import org.hzero.message.api.dto.NoticeDTO;
import org.hzero.message.domain.repository.NoticeRepository;
import org.hzero.message.domain.vo.NoticeCacheVO;
import org.hzero.starter.keyencrypt.core.Encrypt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

@ApiModel("公告基础信息")
@VersionAudit
@ModifyAudit
@Table(
    name = "hmsg_notice"
)
@JsonInclude(Include.NON_NULL)
public class Notice extends AuditDomain {
    public static final String NOTICE_PLATFORM = "GOING-BUY";
    public static final String NOTICE_NEWS = "NEWS";
    public static final String NOTICE_BIDDING = "BIDDING";
    public static final String STATUS_DRAFT = "DRAFT";
    public static final String STATUS_PUBLISHED = "PUBLISHED";
    public static final String STATUS_DELETED = "DELETED";
    public static final String FIELD_NOTICE_ID = "noticeId";
    public static final String FIELD_TITLE = "title";
    public static final String FIELD_PUBLISHED_DATE = "publishedDate";
    public static final String REMIND_TYPE_CODE = "remindTypeCode";
    public static final String RECEIVER_TYPE_CODE = "receiverTypeCode";
    public static final String STATUS_CODE = "statusCode";
    public static final String START_DATE = "startDate";
    public static final String END_DATE = "endDate";
    private static final FastDateFormat DATE_FORMAT = FastDateFormat.getInstance("yyyy-MM-dd");
    private static final FastDateFormat DATE_TIME_FORMAT = FastDateFormat.getInstance("yyyyMMddHHmm");
    private static final Logger logger = LoggerFactory.getLogger(Notice.class);
    @ApiModelProperty("表ID，主键，供其他表做外键")
    @Id
    @GeneratedValue
    @Encrypt
    private Long noticeId;
    @ApiModelProperty(
        value = "语言code",
        required = true
    )
    @NotBlank
    private String lang;
    @ApiModelProperty(
        value = "公告主题",
        required = true
    )
    @NotBlank
    private String title;
    @ApiModelProperty(
        value = "公告发布对象类别(公告、通知),值集：HMSG.NOTICE.RECEIVER_TYPE",
        required = true
    )
    @NotBlank
    private String receiverTypeCode;
    @ApiModelProperty(
        value = "公告类别,值集：HMSG.NOTICE.NOTICE_CATEGORY",
        required = true
    )
    private String noticeCategoryCode;
    @ApiModelProperty(
        value = "公告类型,值集：HMSG.NOTICE.NOTICE_TYPE",
        required = true
    )

    @NotBlank
    private String remindTypeCode;
    @ApiModelProperty(
            value = "提醒方式,值集：HMSG.NOTIFICATION_TYPE",
            required = true
    )

    @NotBlank
    private String noticeTypeCode;
    @ApiModelProperty(
        value = "有效期从",
        required = true
    )
    @NotNull
    private Date startDate;
    private Date endDate;
    @ApiModelProperty(
        value = "租户ID",
        required = true
    )
    @NotNull
    private Long tenantId;
    @ApiModelProperty(
        value = "公告状态，值集：HMSG.NOTICE.STATUS",
        required = true
    )
    @NotBlank
    private String statusCode;
    @ApiModelProperty(
        value = "附件uuid",
        required = true
    )
    private String attachmentUuid;
    @ApiModelProperty("发布时间")
    private Date publishedDate;
    @ApiModelProperty("发布人ID")
    private Long publishedBy;
    @ApiModelProperty("顶部公告标识")
    private Integer stickyFlag;

    public Notice() {
    }

    public static Double getNowTimeScore() {
        return Double.valueOf(DATE_TIME_FORMAT.format(new Date()));
    }

    public void refreshCachePublishedNotices(RedisHelper redisHelper, ObjectMapper objectMapper) {
        if ("PUBLISHED".equals(this.getStatusCode())) {
            redisHelper.zSetRemove(getOrderCacheKey("BIDDING", this.tenantId, this.lang), String.valueOf(this.noticeId));
            redisHelper.zSetRemove(getOrderCacheKey("NEWS", this.tenantId, this.lang), String.valueOf(this.noticeId));
            redisHelper.zSetRemove(getOrderCacheKey("GOING-BUY", this.tenantId, this.lang), String.valueOf(this.noticeId));
            redisHelper.delKey("hmsg:published_notice:" + this.noticeId);
            if (this.endDate != null && this.endDate.getTime() <= System.currentTimeMillis()) {
                return;
            }

            Date showDate = this.startDate.after(this.publishedDate) ? this.startDate : this.publishedDate;
            String showDateTime = DATE_TIME_FORMAT.format(showDate);
            String orderKey = getOrderCacheKey(this.noticeCategoryCode, this.tenantId, this.lang);
            redisHelper.zSetAdd(orderKey, String.valueOf(this.noticeId), Double.parseDouble(showDateTime));
            long expire = (Long)Optional.ofNullable(this.endDate).map((d) -> {
                return d.getTime() - this.startDate.getTime();
            }).orElse(-1L);
            NoticeDTO noticeDTO = (NoticeDTO)CommonConverter.beanConvert(NoticeDTO.class, this);
            NoticeCacheVO noticeCacheVO = new NoticeCacheVO(this.noticeId, this.title, DATE_FORMAT.format(showDate), noticeDTO.getNoticeTypeMeaning());

            try {
                redisHelper.strSet("hmsg:published_notice:" + this.noticeId, objectMapper.writeValueAsString(noticeCacheVO), expire, TimeUnit.MILLISECONDS);
            } catch (JsonProcessingException var11) {
                logger.error(var11.getMessage(), var11);
            }
        }

    }

    public void deleteCachePublishedNotice(RedisHelper redisHelper) {
        String orderKey = getOrderCacheKey(this.noticeCategoryCode, this.tenantId, this.lang);
        redisHelper.zSetRemove(orderKey, String.valueOf(this.noticeId));
        redisHelper.delKey("hmsg:published_notice:" + this.noticeId);
    }

    public static String getOrderCacheKey(String noticeCategoryCode, Long tenantId, String lang) {
        return "hmsg:published_notice_order:" + tenantId + ":" + noticeCategoryCode + ":" + lang;
    }

    public static Boolean checkStatusCode(String currentStatusCode, String statusCode) {
        Boolean situationA = currentStatusCode.equals("DRAFT") && (statusCode.equals("DELETED") || statusCode.equals("PUBLISHED"));
        Boolean situationB = currentStatusCode.equals("DELETED") && statusCode.equals("DRAFT");
        Boolean situationC = currentStatusCode.equals("PUBLISHED") && statusCode.equals("DELETED");
        return !situationA && !situationB && !situationC ? Boolean.FALSE : Boolean.TRUE;
    }

    public static Notice updateStatus(NoticeRepository noticeRepository, Long noticeId, String statusCode) {
        Notice notice = (Notice)noticeRepository.selectByPrimaryKey(noticeId);
        Assert.notNull(notice, "error.data_not_exists");
        notice.setStatusCode(statusCode);
        if ("PUBLISHED".equals(statusCode)) {
            CustomUserDetails userDetails = DetailsHelper.getUserDetails();
            notice.setPublishedDate(new Date());
            notice.setPublishedBy(userDetails.getUserId());
        }

        noticeRepository.updateByPrimaryKey(notice);
        return notice;
    }

    public static FastDateFormat getDateFormat() {
        return DATE_FORMAT;
    }

    public static FastDateFormat getDateTimeFormat() {
        return DATE_TIME_FORMAT;
    }

    public static Logger getLogger() {
        return logger;
    }

    public Long getNoticeId() {
        return this.noticeId;
    }

    public Notice setNoticeId(Long noticeId) {
        this.noticeId = noticeId;
        return this;
    }

    public String getLang() {
        return this.lang;
    }

    public Notice setLang(String lang) {
        this.lang = lang;
        return this;
    }

    public String getTitle() {
        return this.title;
    }

    public Notice setTitle(String title) {
        this.title = title;
        return this;
    }

    public String getReceiverTypeCode() {
        return this.receiverTypeCode;
    }

    public Notice setReceiverTypeCode(String receiverTypeCode) {
        this.receiverTypeCode = receiverTypeCode;
        return this;
    }

    public String getNoticeCategoryCode() {
        return this.noticeCategoryCode;
    }

    public Notice setNoticeCategoryCode(String noticeCategoryCode) {
        this.noticeCategoryCode = noticeCategoryCode;
        return this;
    }

    public String getNoticeTypeCode() {
        return this.noticeTypeCode;
    }

    public Notice setNoticeTypeCode(String noticeTypeCode) {
        this.noticeTypeCode = noticeTypeCode;
        return this;
    }

    public Date getStartDate() {
        return this.startDate;
    }

    public Notice setStartDate(Date startDate) {
        this.startDate = startDate;
        return this;
    }

    public Date getEndDate() {
        return this.endDate;
    }

    public Notice setEndDate(Date endDate) {
        this.endDate = endDate;
        return this;
    }

    public Long getTenantId() {
        return this.tenantId;
    }

    public Notice setTenantId(Long tenantId) {
        this.tenantId = tenantId;
        return this;
    }

    public String getStatusCode() {
        return this.statusCode;
    }

    public Notice setStatusCode(String statusCode) {
        this.statusCode = statusCode;
        return this;
    }

    public String getAttachmentUuid() {
        return this.attachmentUuid;
    }

    public Notice setAttachmentUuid(String attachmentUuid) {
        this.attachmentUuid = attachmentUuid;
        return this;
    }

    public Date getPublishedDate() {
        return this.publishedDate;
    }

    public Notice setPublishedDate(Date publishedDate) {
        this.publishedDate = publishedDate;
        return this;
    }

    public Long getPublishedBy() {
        return this.publishedBy;
    }

    public Notice setPublishedBy(Long publishedBy) {
        this.publishedBy = publishedBy;
        return this;
    }

    public Integer getStickyFlag() {
        return this.stickyFlag;
    }

    public Notice setStickyFlag(Integer stickyFlag) {
        this.stickyFlag = stickyFlag;
        return this;
    }

    public static String getNoticePlatform() {
        return NOTICE_PLATFORM;
    }

    public String getRemindTypeCode() {
        return remindTypeCode;
    }

    public void setRemindTypeCode(String remindTypeCode) {
        this.remindTypeCode = remindTypeCode;
    }
}
