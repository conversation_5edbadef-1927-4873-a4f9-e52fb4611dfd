<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.hzero</groupId>
        <artifactId>hzero-parent</artifactId>
        <version>1.4.7.RELEASE</version>
    </parent>
    <groupId>org.hzero</groupId>
    <artifactId>o2-basic-message-be</artifactId>
    <version>0.1.0-SNAPSHOT</version>
    <name>o2-basic-message-be</name>

    <properties>
        <dockerfile.repository>harbor.djicorp.com</dockerfile.repository>
        <registry.project>sales-order-center</registry.project>
        <image.tag>latest</image.tag>
    </properties>

    <dependencies>
        <!-- 数据库驱动 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!-- 依赖的服务 -->
        <dependency>
            <groupId>org.hzero</groupId>
            <artifactId>hzero-message-saas</artifactId>
            <exclusions>
                <!--排除spring boot 自带日志依赖 会与log4j 冲突-->
                <exclusion>
                    <artifactId>spring-boot-starter-logging</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>
       
        
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hzero.starter</groupId>
            <artifactId>hzero-starter-config-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hzero.starter</groupId>
            <artifactId>hzero-starter-sms-aliyun</artifactId>
        </dependency>

        <!--依赖LOG4J-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
            <version>0.9.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>net.logstash.logback</groupId>
                    <artifactId>logstash-logback-encoder</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.opentracing.contrib</groupId>
            <artifactId>opentracing-spring-web-autoconfigure</artifactId>
            <version>0.3.0</version>
        </dependency>


        <!-- https://mvnrepository.com/artifact/org.projectlombok/lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.14</version>
            <scope>provided</scope>
        </dependency>

        <!--    集成dji网关请求-->
        <dependency>
            <groupId>com.dji.dava</groupId>
            <artifactId>dava-gateway-core</artifactId>
            <version>2.0.0.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>org.hzero.boot</groupId>
            <artifactId>hzero-boot-message</artifactId>
        </dependency>

    </dependencies>

    <repositories>
        <!-- 外网仓库 -->
        <repository>
            <id>HzeroRelease-external</id>
            <name>Hzero-Release external Repository</name>
            <url>http://nexus.saas.hand-china.com/content/repositories/Hzero-Release/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
        <repository>
            <id>HzeroSnapshot-external</id>
            <name>Hzero-Snapshot external Repository</name>
            <url>http://nexus.saas.hand-china.com/content/repositories/Hzero-Snapshot/</url>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
        <repository>
            <id>HandPublic-external</id>
            <name>Hand-Public external Repository</name>
            <url>http://nexus.saas.hand-china.com/content/repositories/public/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </releases>
        </repository>
        <repository>
            <id>aliyunmaven-external</id>
            <name>aliyunmaven-external-repository</name>
            <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
        </repository>

        <repository>
            <id>dji-nexus-external</id>
            <name>djicorp-external-repository</name>
            <url>http://nexus.aasky.net:8081/nexus/content/groups/public/</url>
        </repository>
        <repository>
            <id>hippiusSnapshot</id>
            <url>http://nexus.saas.hand-china.com/content/repositories/hippiusSnapshot/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
        <repository>
            <id>hippiusRelease</id>
            <url>http://nexus.saas.hand-china.com/content/repositories/hippiusRelease/</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>HoneRelease</id>
            <name>Hzero-Release Repository</name>
            <url>http://nexus.saas.hand-china.com/repository/hone-release/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>HoneSnapshot</id>
            <name>Hone-Snapshot Repository</name>
            <url>http://nexus.saas.hand-china.com/repository/hone-snapshot/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>

    <pluginRepositories>
        <!--  外网仓库 -->
        <pluginRepository>
            <id>dji-external-nexus</id>
            <name>djicorp-external-repository</name>
            <url>http://nexus.aasky.net:8081/nexus/content/groups/public/</url>
        </pluginRepository>
        <pluginRepository>
            <id>central external</id>
            <name>Central external Repository</name>
            <url>http://maven.aliyun.com/nexus/content/repositories/central/</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>

        <pluginRepository>
            <id>aliyunmaven external</id>
            <name>aliyunmaven-external-repository</name>
            <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
        </pluginRepository>
    </pluginRepositories>

    <profiles>
        <!-- 部署容器环境 -->
        <profile>
            <id>local</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <dockerfile.skip>true</dockerfile.skip>
                <springboot.skip>false</springboot.skip>
                <assembly.skip>true</assembly.skip>
                <maven.javadoc.skip>true</maven.javadoc.skip>
            </properties>
        </profile>
        <profile>
            <id>docker</id>
            <properties>
                <dockerfile.skip>false</dockerfile.skip>
                <springboot.skip>false</springboot.skip>
                <assembly.skip>true</assembly.skip>
                <maven.javadoc.skip>true</maven.javadoc.skip>
            </properties>
        </profile>
        <profile>
            <id>linux</id>
            <properties>
                <dockerfile.skip>true</dockerfile.skip>
                <springboot.skip>true</springboot.skip>
                <assembly.skip>false</assembly.skip>
            </properties>
        </profile>
    </profiles>
    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!-- 特别注意，如无必要，请勿添加maven-source-plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <fork>true</fork>
                    <skip>${springboot.skip}</skip>
                </configuration>
                <executions>
                    <execution>
                        <id>springboot-package</id>
                        <phase>package</phase>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- assembly package -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <executions>
                    <execution>
                        <id>make-assembly-docker</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                        <configuration>
                            <skipAssembly>${assembly.skip}</skipAssembly>
                            <descriptors>
                                <descriptor>src/main/scripts/assembly/assembly-linux.xml</descriptor>
                            </descriptors>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!-- docker package -->
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <configuration>
                    <repository>${dockerfile.repository}/${registry.project}/${project.artifactId}</repository>
                    <tag>${dockerfile.tag}</tag>
                    <buildArgs>
                        <JAR_FILE>${project.artifactId}.jar</JAR_FILE>
                        <IMAGE_HOST>${dockerfile.repository}</IMAGE_HOST>
                    </buildArgs>
                    <skip>${dockerfile.skip}</skip>
                </configuration>
                <executions>
                    <execution>
                        <id>dockerfile-build</id>
                        <phase>package</phase>
                        <goals>
                            <goal>build</goal>
                            <goal>push</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- 特别注意，如无必要，请勿添加maven-source-plugin -->
            <plugin>
                <!-- mvn clean archetype:create-from-project -->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>3.0.1</version>
                <configuration>
                    <propertyFile>archetype/archetype.properties</propertyFile>
                </configuration>
            </plugin>
            <plugin>
                <groupId>fr.jcgay.maven.plugins</groupId>
                <artifactId>buildplan-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>


