package org.hzero.message.api.controller.v1;

import io.choerodon.core.iam.*;
import io.choerodon.swagger.annotation.*;
import io.swagger.annotations.*;
import lombok.*;
import lombok.experimental.*;
import org.hzero.boot.message.entity.*;
import org.hzero.core.util.*;
import org.hzero.message.app.service.*;
import org.hzero.message.config.*;
import org.springframework.http.*;
import org.springframework.validation.annotation.*;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/20
 */
@Api(tags = DjiMessageSwaggerApiConfig.SEND_CALL)
@RestController("DjiSendCallController.v1")
@Validated
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequestMapping("/v1/dji-send-call")
public class DjiSendCallController {

    DjiSendCallService djiSendCallService;

    @ApiOperation(value = "电话报警")
    @Permission(level = ResourceLevel.ORGANIZATION)
    @PostMapping("/voice-notification")
    public ResponseEntity<String> voiceNotification(@RequestBody UMSBaseMsgDTO umsBaseMsgDTO) throws Exception {
        return Results.success(djiSendCallService.voiceNotification(umsBaseMsgDTO));
    }

    @ApiOperation(value = "获取用户组编码")
    @Permission(level = ResourceLevel.ORGANIZATION)
    @GetMapping("/get-user-group")
    public ResponseEntity<List<Receiver>> getUserGroupList(@RequestParam @ApiParam(value = "用户组编码", required = true) String userGroup) {
        return Results.success(djiSendCallService.getUserGroupList(userGroup));
    }
}
