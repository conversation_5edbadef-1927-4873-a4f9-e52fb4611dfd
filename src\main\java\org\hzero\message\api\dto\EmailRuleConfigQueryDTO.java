

package org.hzero.message.api.dto;

import io.swagger.annotations.*;
import lombok.*;
import lombok.experimental.*;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("邮件规则配置查询DTO")
public class EmailRuleConfigQueryDTO {

    @ApiModelProperty(value = "场景id")
    private String sceneId;

    @ApiModelProperty(value = "组别id")
    private Long groupId;

    @ApiModelProperty(value = "语种")
    private String language;

    @ApiModelProperty(value = "发件邮箱")
    private String sendMailBox;

    @ApiModelProperty(value = "消息模板")
    private String templateCode;

    @ApiModelProperty(value = "当前登录语种")
    private String lang;
}
