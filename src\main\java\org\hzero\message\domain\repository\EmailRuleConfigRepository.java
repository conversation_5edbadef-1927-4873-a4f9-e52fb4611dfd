package org.hzero.message.domain.repository;

import io.choerodon.core.domain.*;
import io.choerodon.mybatis.pagehelper.domain.*;
import org.apache.ibatis.annotations.*;
import org.hzero.message.api.dto.*;
import org.hzero.message.domain.entity.*;
import org.hzero.message.domain.vo.*;
import org.hzero.mybatis.base.*;

import java.util.*;

/**
 * 邮件规则配置表资源库
 *
 * <AUTHOR> 2024-06-14 15:38:08
 */
public interface EmailRuleConfigRepository extends BaseRepository<EmailRuleConfig> {

    /**
     * 获取邮件规则配置列表
     * @param pageRequest 分页参数
     * @param queryDTO 查询条件
     * @return 结果
     */
    Page<EmailRuleConfigResVO> queryEmailRuleConfigListPage(PageRequest pageRequest, EmailRuleConfigQueryDTO queryDTO);

    /**
     * 导出邮件规则配置列表
     * @param queryDTO 获取邮件规则配置列表
     * @return 结果
     */
    List<EmailRuleConfigExportDTO> export(EmailRuleConfigQueryDTO queryDTO);

    /**
     * 导入查询数据
     * @param setList 入参
     * @return 返参
     */
    List<EmailRuleConfigResImportVO> queryEmailRuleConfigListImport(Set<EmailRuleConfigInputDTO> setList);

    /**
     * 批量插入
     * @param list 订单行
     */
    void batchInsertByNative(List<EmailRuleConfig> list);

    /**
     * 批量更新
     * @param list 订单行
     */
    void batchUpdateByNative(List<EmailRuleConfig> list);

    /**
     * 查询满足同时条件的模版列表+模版信息
     * @param emailRuleConfigQueryDTOList
     * @return
     */
    List<EmailConfigTemplateInfoVO> queryEmailRuleConfigCmList(List<EmailRuleConfigCmQueryDTO> emailRuleConfigQueryDTOList);
}
