<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2020. DJI All Rights Reserved.
  ~
  ~ Author <EMAIL>
  -->
<configuration status="off" monitorInterval="1800" packages="org.apache.logging.log4j.core">
    <properties>
        <property name="LOG_PATH">/data/logs/app/</property>
        <!-- 应用名称 -->
        <!--<property name="APP_NAME">app_name</property> -->
        <!-- 应用日志 -->
        <property name="LOG_NAME">o2</property>
        <!-- hzero日志 -->
        <property name="LOG_HZERO">hzero</property>
        <!-- 全局日志 -->
        <property name="LOG_SYS">system</property>
        <!--<property name="MACHINE_ID">techni</property>-->
    </properties>

    <appenders>
        <Console name="STDOUT" target="SYSTEM_OUT" follow="true">
            <PatternLayout pattern="[%d{yyyy-MM-dd HH:mm:ss} %5p [%t] %c{1.}:%L] %m%n" />
        </Console>
        <Console name="JsonFormatStdout" target="SYSTEM_OUT" follow="true">
            <PatternLayout charset="UTF-8">
                <Pattern>
                    {"@timestamp":"%d{ISO8601}", "log_uuid": "%mdc{log_uuid}", "invoke_id":"%mdc{invoke_id}","user":"%mdc{user}","status":"%mdc{status}","trace_id": "%mdc{trace_id}",  "client_ip":"%mdc{client_ip}", "server_ip":"%mdc{server_ip}", "server_host":"%mdc{server_host}", "referer":"%mdc{referer}", "duration":"%mdc{duration}","user_agent":"%mdc{user_agent}","path":"%mdc{uri}", "method":"%mdc{method}", "level":"%level", "class":"%c{36} %M %L", "request_id":"%mdc{request_id}","message":"%enc{%m}{JSON}", "exception":"%enc{%ex}{JSON}", "thread": "%thread"}%n
                </Pattern>
            </PatternLayout>
        </Console>
        <RollingRandomAccessFile name="o2"
                                 filePermissions="rw-------" fileName="${LOG_PATH}/${LOG_NAME}-${sys:MACHINE_ID:-yaya}.log"
                                 filePattern="${LOG_PATH}/${LOG_NAME}-${sys:MACHINE_ID:-yaya}-%d{yyyyMMdd}-%i.log.gz">
            <PatternLayout pattern="[%d{yyyy-MM-dd HH:mm:ss} %5p [%t] %c:%L] %m%n" />
            <Policies>
                <TimeBasedTriggeringPolicy />
            </Policies>
        </RollingRandomAccessFile>
        <RollingRandomAccessFile name="hzero"
                                 filePermissions="rw-------" fileName="${LOG_PATH}/${LOG_HZERO}-${sys:MACHINE_ID:-yaya}.log"
                                 filePattern="${LOG_PATH}/${LOG_HZERO}-${sys:MACHINE_ID:-yaya}-%d{yyyyMMdd}-%i.log.gz">
            <PatternLayout pattern="[%d{yyyy-MM-dd HH:mm:ss} %5p [%t] %c:%L] %m%n" />
            <Policies>
                <TimeBasedTriggeringPolicy modulate="true" />
            </Policies>
        </RollingRandomAccessFile>
        <RollingRandomAccessFile name="sys"
                                 filePermissions="rw-------"
                                 fileName="${LOG_PATH}/filebeat-${LOG_SYS}-${sys:MACHINE_ID:-yaya}.log"
                                 filePattern="${LOG_PATH}/filebeat-${LOG_SYS}-${sys:MACHINE_ID:-yaya}-%d{yyyyMMdd}-%i.log.gz">
            <PatternLayout pattern="[%d{yyyy-MM-dd HH:mm:ss} %5p [%t] %c:%L] %m%n" />
            <Policies>
                <TimeBasedTriggeringPolicy />
                <SizeBasedTriggeringPolicy size="512MB" />
            </Policies>
            <DefaultRolloverStrategy max="99" />
        </RollingRandomAccessFile>

    </appenders>
    <loggers>
        <!-- 根据自己的需要添加logger & appender -->
        <!-- hzero framework log -->
        <logger name="org.hzero" level="info">
            <appender-ref ref="hzero" />
        </logger>
        <logger name="org.hzero.boot.platform.lov" level="info">
            <appender-ref ref="hzero" />
        </logger>
        <!-- application log -->
        <logger name="org.apache.ibatis" level="info">
            <appender-ref ref="o2" />
        </logger>
        <logger name="com.dji" level="info">
            <appender-ref ref="o2" />
        </logger>
        <!-- other framework -->
        <logger name="org.springframework" level="info">
            <appender-ref ref="sys" />
        </logger>
        <logger name="com.netflix.discovery" level="info">
            <appender-ref ref="sys" />
        </logger>
        <logger name="io.choerodon" level="info">
            <appender-ref ref="sys" />
        </logger>
        <root>
            <!--<appender-ref ref="STDOUT" />-->
            <appender-ref ref="JsonFormatStdout" />
            <appender-ref ref="sys" />
            <appender-ref ref="o2" level="info" />

        </root>
    </loggers>
</configuration>
