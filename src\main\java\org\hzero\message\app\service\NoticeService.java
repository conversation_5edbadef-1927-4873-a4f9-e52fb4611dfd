

package org.hzero.message.app.service;

import io.choerodon.core.domain.Page;
import io.choerodon.mybatis.pagehelper.domain.PageRequest;
import java.util.List;
import org.hzero.message.api.dto.NoticeDTO;
import org.hzero.message.domain.entity.Notice;

public interface NoticeService {
    NoticeDTO createNotice(NoticeDTO noticeDTO);

    NoticeDTO updateNotice(NoticeDTO noticeDTO);

    Notice deleteNotice(Long organizationId, Long noticeId);

    Notice publicNotice(Long organizationId, Long noticeId);

    Notice revokeNotice(Long organizationId, Long noticeId);

    Page<NoticeDTO> pageNoticeTitle(String category, String title, Long organizationId, PageRequest pageRequest);

    List<NoticeDTO> listUserAnnouncement(NoticeDTO noticeDTO);

    NoticeDTO topAnnouncement(NoticeDTO noticeDTO);

    Page<NoticeDTO> pageNotice(PageRequest pageRequest, NoticeDTO noticeDTO);

    NoticeDTO bulletin();
}
