<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.hzero</groupId>
        <artifactId>hzero-parent</artifactId>
        <version>1.4.7.RELEASE</version>
    </parent>
    <groupId>org.hzero</groupId>
    <artifactId>o2-basic-message-be</artifactId>
    <version>0.1.0-SNAPSHOT</version>
    <name>o2-basic-message-be</name>

    <properties>
        <dockerfile.repository>harbor.djicorp.com</dockerfile.repository>
        <registry.project>sales-order-center</registry.project>

        <sonar.java.coveragePlugin>jacoco</sonar.java.coveragePlugin>
        <sonar.dynamicAnalysis>reuseReports</sonar.dynamicAnalysis>
        <sonar.jacoco.reportPath>${project.basedir}/target/jacoco.exec</sonar.jacoco.reportPath>
        <sonar.language>java</sonar.language>
        <sonar.host.url>https://sonarqube.djicorp.com/</sonar.host.url>
        <sonar.projectKey>o2-basic-message-be</sonar.projectKey>
        <sonar.projectName>o2-basic-message-be</sonar.projectName>
        <sonar.moduleKey>${project.groupId}:${project.artifactId}</sonar.moduleKey>
        <dockerfile.repository>harbor.djicorp.com</dockerfile.repository>
    </properties>

    <dependencies>
        <!-- 数据库驱动 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!-- 依赖的服务 -->
        <dependency>
            <groupId>org.hzero</groupId>
            <artifactId>hzero-message-saas</artifactId>
            <exclusions>
                <!--排除spring boot 自带日志依赖 会与log4j 冲突-->
                <exclusion>
                    <artifactId>spring-boot-starter-logging</artifactId>
                    <groupId>org.springframework.boot</groupId>
                </exclusion>
            </exclusions>
        </dependency>
       
        
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hzero.starter</groupId>
            <artifactId>hzero-starter-config-client</artifactId>
            <optional>true</optional>
        </dependency>
        <dependency>
            <groupId>org.hzero.starter</groupId>
            <artifactId>hzero-starter-sms-aliyun</artifactId>
        </dependency>
        <!--任务调度 -->
        <dependency>
            <groupId>org.hzero.boot</groupId>
            <artifactId>hzero-boot-scheduler</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.velocity</groupId>
                    <artifactId>velocity</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--依赖LOG4J-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
            <version>0.9.1-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>net.logstash.logback</groupId>
                    <artifactId>logstash-logback-encoder</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>io.opentracing.contrib</groupId>
            <artifactId>opentracing-spring-web-autoconfigure</artifactId>
            <version>0.3.0</version>
        </dependency>


        <!-- https://mvnrepository.com/artifact/org.projectlombok/lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.14</version>
            <scope>provided</scope>
        </dependency>

        <!--    集成dji网关请求-->
        <dependency>
            <groupId>com.dji.dava</groupId>
            <artifactId>dava-gateway-core</artifactId>
            <version>2.0.0.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.dji.gt</groupId>
            <artifactId>gt-open-sdk-core</artifactId>
            <version>2.0.0</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.4</version>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.0.1</version>
        </dependency>

        <!--   H0相关依赖     -->
        <dependency>
            <groupId>org.hzero.starter</groupId>
            <artifactId>hzero-starter-mybatis-mapper</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hzero.boot</groupId>
            <artifactId>hzero-boot-platform</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hzero.starter</groupId>
            <artifactId>hzero-starter-dynamic-route</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hzero.starter</groupId>
            <artifactId>hzero-starter-feign-replay</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hzero.starter</groupId>
            <artifactId>hzero-starter-hitoa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hzero.starter</groupId>
            <artifactId>hzero-starter-export</artifactId>
            <version>1.5.2.RELEASE</version>
        </dependency>

        <!-- 单元测试 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!--mock工具-->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>5.6.2</version>
            <scope>test</scope>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.junit.jupiter/junit-jupiter-api -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <version>5.6.2</version>
            <scope>test</scope>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.junit.jupiter/junit-jupiter-engine -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>5.6.2</version>
            <scope>test</scope>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.junit.jupiter/junit-jupiter-params -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-params</artifactId>
            <version>5.6.2</version>
            <scope>test</scope>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.junit.platform/junit-platform-commons -->
        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-commons</artifactId>
            <version>1.6.2</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.junit.platform/junit-platform-engine -->
        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-engine</artifactId>
            <version>1.6.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>2.23.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>2.0.0-RC.3</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>2.0.0-RC.3</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.hzero.boot</groupId>
            <artifactId>hzero-boot-import</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.2</version>
        </dependency>

        <dependency>
            <groupId>org.hzero.boot</groupId>
            <artifactId>hzero-boot-message</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.velocity</groupId>
                    <artifactId>velocity</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.velocity</groupId>
            <artifactId>velocity-engine-core</artifactId>
            <version>2.3</version>
        </dependency>
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.14.3</version>
        </dependency>

    </dependencies>

    <repositories>
        <!-- 内网仓库 -->
        <repository>
            <id>HzeroRelease</id>
            <name>Hzero-Release Repository</name>
            <url>https://nexus.djicorp.com/nexus/content/repositories/HzeroRelease/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
        <repository>
            <id>HzeroSnapshot</id>
            <name>Hzero-Snapshot Repository</name>
            <url>https://nexus.djicorp.com/nexus/content/repositories/HzeroSnapshot/</url>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
        <repository>
            <id>HandPublic</id>
            <name>Hand-Public Repository</name>
            <url>https://nexus.djicorp.com/nexus/content/repositories/HandPublic/</url>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </releases>
        </repository>
        <repository>
            <id>hippiusSnapshot</id>
            <url>https://nexus.djicorp.com/nexus/content/repositories/hippiusSnapshot/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
        <repository>
            <id>hippiusRelease</id>
            <url>https://nexus.djicorp.com/nexus/content/repositories/hippiusRelease/</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>HoneRelease</id>
            <name>Hzero-Release Repository</name>
            <url>https://nexus.djicorp.com/nexus/content/repositories/hone-release/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>HoneSnapshot</id>
            <name>Hone-Snapshot Repository</name>
            <url>https://nexus.djicorp.com/nexus/content/repositories/hone-snapshot/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
        <!--默认内网 勿删注释-->
        <repository>
            <id>dji-nexus</id>
            <name>djicorp-repository</name>
            <url>https://nexus.djicorp.com/nexus/content/groups/public/</url>
        </repository>

    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>central</id>
            <name>Central Repository</name>
            <url>https://nexus.djicorp.com/nexus/content/repositories/central/</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
        <pluginRepository>
            <id>dji-nexus</id>
            <name>djicorp-repository</name>
            <url>https://nexus.djicorp.com/nexus/content/groups/public/</url>
        </pluginRepository>
    </pluginRepositories>

    <profiles>
        <!-- 部署容器环境 -->
        <profile>
            <id>local</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <dockerfile.skip>true</dockerfile.skip>
                <springboot.skip>false</springboot.skip>
                <assembly.skip>true</assembly.skip>
                <maven.javadoc.skip>true</maven.javadoc.skip>
            </properties>
        </profile>
        <profile>
            <id>docker</id>
            <properties>
                <dockerfile.skip>false</dockerfile.skip>
                <springboot.skip>false</springboot.skip>
                <assembly.skip>true</assembly.skip>
                <maven.javadoc.skip>true</maven.javadoc.skip>
            </properties>
        </profile>
        <profile>
            <id>linux</id>
            <properties>
                <dockerfile.skip>true</dockerfile.skip>
                <springboot.skip>true</springboot.skip>
                <assembly.skip>false</assembly.skip>
            </properties>
        </profile>
    </profiles>
    <build>
        <finalName>${project.artifactId}-${project.version}</finalName>
        <plugins>
            <!-- 特别注意，如无必要，请勿添加maven-source-plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <fork>true</fork>
                    <skip>${springboot.skip}</skip>
                </configuration>
                <executions>
                    <execution>
                        <id>springboot-package</id>
                        <phase>package</phase>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- assembly package -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <executions>
                    <execution>
                        <id>make-assembly-docker</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                        <configuration>
                            <skipAssembly>${assembly.skip}</skipAssembly>
                            <descriptors>
                                <descriptor>src/main/scripts/assembly/assembly-linux.xml</descriptor>
                            </descriptors>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!-- docker package -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>3.0.0</version>
                <executions>
                    <execution>
                        <id>chmod</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                        <configuration>
                            <executable>chmod</executable>
                            <arguments>
                                <argument>+x</argument>
                                <!--kaniko打包脚本文件位置-->
                                <argument>${basedir}/docker-package.sh</argument>
                            </arguments>
                        </configuration>
                    </execution>
                    <execution>
                        <id>dockerfile-build</id>
                        <phase>package</phase>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                        <configuration>
                            <!--kaniko打包脚本文件位置-->
                            <executable>${basedir}/docker-package.sh</executable>
                            <environmentVariables>
                                <JAR_FILE>${project.artifactId}-${project.version}.jar</JAR_FILE>
                                <IMAGE_HOST>${dockerfile.repository}</IMAGE_HOST>
                                <REGISTRY_PROJECT>${registry.project}</REGISTRY_PROJECT>
                            </environmentVariables>
                        </configuration>
                    </execution>
                </executions>
                <configuration>
                    <skip>${dockerfile.skip}</skip>
                    <useMavenLogger>true</useMavenLogger>
                </configuration>
            </plugin>
            <!-- 特别注意，如无必要，请勿添加maven-source-plugin -->
            <plugin>
                <!-- mvn clean archetype:create-from-project -->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-archetype-plugin</artifactId>
                <version>3.0.1</version>
                <configuration>
                    <propertyFile>archetype/archetype.properties</propertyFile>
                </configuration>
            </plugin>
            <plugin>
                <groupId>fr.jcgay.maven.plugins</groupId>
                <artifactId>buildplan-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>


