package org.hzero.message.infra.mapper;

import org.apache.ibatis.annotations.Param;
import org.hzero.boot.message.entity.Receiver;

import java.util.List;

/**
 * description
 *
 * <AUTHOR> 2021/03/18 15:14
 */
public interface DjiUserGroupMapper {
	/**
	 *
	 * 邮件根据用户组查询ad
	 *
	 * <AUTHOR>
	 * @param groupCode 用户组编码
	 * @return java.util.List<org.hzero.boot.message.entity.Receiver>
	 */
	List<Receiver> selectUserGroupList(@Param(value = "groupCode") String groupCode);
}
