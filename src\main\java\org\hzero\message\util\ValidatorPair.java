package org.hzero.message.util;

import org.hzero.boot.imported.domain.entity.*;

/**
 * 验证数据时，绑定原始数据和转换后的数据
 *
 * <AUTHOR> 2021/3/10 18:05
 **/
public class ValidatorPair<T> {

    private final T target;

    private final ImportData source;

    private ValidatorPair(T target, ImportData source) {
        this.target = target;
        this.source = source;
    }

    public static <T> ValidatorPair<T> of(T target, ImportData source) {
        return new ValidatorPair<>(target, source);
    }

    public T target() {
        return this.target;
    }

    public ImportData source() {
        return this.source;
    }
}
