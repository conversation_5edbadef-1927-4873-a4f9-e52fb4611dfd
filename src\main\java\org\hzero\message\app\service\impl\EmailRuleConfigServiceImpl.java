package org.hzero.message.app.service.impl;

import cn.hutool.core.bean.*;
import com.alibaba.fastjson.*;
import com.ctrip.framework.apollo.*;
import com.dji.dava.dto.Result;
import com.dji.dava.gateway.cloudbus.*;
import com.dji.dava.utils.*;
import com.google.common.collect.*;
import io.choerodon.core.domain.*;
import io.choerodon.core.exception.*;
import io.choerodon.core.oauth.*;
import io.choerodon.mybatis.pagehelper.domain.*;
import lombok.*;
import lombok.experimental.*;
import lombok.extern.slf4j.*;
import org.apache.commons.collections.*;
import org.apache.commons.lang3.*;
import org.hzero.boot.message.entity.*;
import org.hzero.boot.platform.lov.adapter.*;
import org.hzero.boot.platform.lov.annotation.*;
import org.hzero.boot.platform.lov.dto.*;
import org.hzero.core.base.*;
import org.hzero.core.helper.*;
import org.hzero.core.message.*;
import org.hzero.message.api.dto.*;
import org.hzero.message.app.service.*;
import org.hzero.message.constant.*;
import org.hzero.message.domain.entity.*;
import org.hzero.message.domain.entity.Message;
import org.hzero.message.domain.entity.MessageTemplate;
import org.hzero.message.domain.repository.*;
import org.hzero.message.domain.vo.*;
import org.hzero.message.infra.constant.*;
import org.hzero.message.infra.feign.*;
import org.hzero.mybatis.domian.*;
import org.hzero.mybatis.util.*;
import org.jetbrains.annotations.*;
import org.springframework.stereotype.*;
import org.springframework.transaction.annotation.*;

import java.util.*;
import java.util.concurrent.*;
import java.util.function.*;
import java.util.regex.*;
import java.util.stream.*;

/**
 * 邮件规则配置表应用服务默认实现
 *
 * <AUTHOR> 2024-06-14 15:38:08
 */
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@Service
public class EmailRuleConfigServiceImpl implements EmailRuleConfigService {

    private static final String SERVER_CODE = "HZERO";
    private static final String DJICM_BRAND = "DJICM_BRAND";
    private static final String QUERY_ALL_CUSTOMER_GROUP = "QUERY_ALL_CUSTOMER_GROUP";
    private static final String SYSTEM_CODE = "o2-basic-message-be";
    private static final String SYSTEM_NAME = "消息服务";
    private static final String PATTERN = "^[a-zA-Z_]*$";
    private static final String REGEX = "\\$\\{.*?\\}";

    private final EmailRuleConfigRepository emailRuleConfigRepository;
    private final MessageTemplateRepository messageTemplateRepository;
    private final EmailServerRepository emailServerRepository;
    private final LovAdapter lovAdapter;
    private final RemoteInvoke rmiClient;
    private final DjiInterfaceFeign djiInterfaceFeign;
    private final EmailSendService emailSendService;
    private final GtSendService sendService;

    @Override
    public Page<EmailRuleConfigResVO> queryEmailRuleConfigListPage(PageRequest pageRequest, EmailRuleConfigQueryDTO queryDTO) {
        return emailRuleConfigRepository.queryEmailRuleConfigListPage(pageRequest, queryDTO);
    }

    @Override
    public EmailRuleConfigResVO findEmailRuleConfigDetail(String ruleConfigId) {
        EmailRuleConfig emailRuleConfig = getEmailRuleConfig(ruleConfigId);
        EmailRuleConfigResVO res = new EmailRuleConfigResVO();
        BeanUtil.copyProperties(emailRuleConfig, res);
        return res;
    }

    @NotNull
    private EmailRuleConfig getEmailRuleConfig(String ruleConfigId) {
        EmailRuleConfig emailRuleConfig = emailRuleConfigRepository.selectByPrimaryKey(ruleConfigId);
        if (Objects.isNull(emailRuleConfig) || Boolean.TRUE.equals(emailRuleConfig.getDeleted())) {
            throw new CommonException(MessageAccessor.getMessage(ErrorCodeConstant.ERROR_DATA_VALID, LanguageHelper.locale()).desc());
        }
        return emailRuleConfig;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String create(EmailRuleConfigSaveDTO saveDTO) {
        EmailRuleConfig emailRuleConfig = new EmailRuleConfig();
        BeanUtil.copyProperties(saveDTO, emailRuleConfig);
        // 校验数据
        verifyData(emailRuleConfig);
        emailRuleConfig.setRuleConfigId(UuidUtils.getLongUUID());
        emailRuleConfigRepository.insertSelective(emailRuleConfig);
        return emailRuleConfig.getRuleConfigId();
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String update(EmailRuleConfigUpdateDTO updateDTO) {
        EmailRuleConfig emailRuleConfig = getEmailRuleConfig(updateDTO.getRuleConfigId());
        BeanUtil.copyProperties(updateDTO, emailRuleConfig);
        // 校验数据
        verifyData(emailRuleConfig);
        emailRuleConfigRepository.updateByPrimaryKeySelective(emailRuleConfig);
        return emailRuleConfig.getRuleConfigId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(String ruleConfigId) {
        EmailRuleConfig emailRuleConfig = getEmailRuleConfig(ruleConfigId);
        emailRuleConfig.setDeleted(Boolean.TRUE);
        emailRuleConfig.setLastUpdateDate(new Date());
        emailRuleConfig.setLastUpdatedBy(DetailsHelper.getUserDetails().getUserId());
        emailRuleConfigRepository.updateByPrimaryKeySelective(emailRuleConfig);
        return Boolean.TRUE;
    }

    @Override
    @ProcessLovValue
    public List<EmailRuleConfigExportDTO> export(EmailRuleConfigQueryDTO queryDTO) {
        String defaultLanguage = LanguageHelper.getDefaultLanguage();
        queryDTO.setLang(defaultLanguage);
        log.info("export入参:{}", JSON.toJSONString(queryDTO));
        return emailRuleConfigRepository.export(queryDTO);
    }

    @Override
    public List<OrderCustomerGroupVO> queryCustomerGroupList() {
        String url = ConfigService.getAppConfig().getProperty(QUERY_ALL_CUSTOMER_GROUP, "");
        Result<String> responseStr = rmiClient.getParams(url, "",
                Maps.newHashMap(), Maps.newHashMap(), new TypeReference<Result<String>>() {
                });
        if (responseStr == null || responseStr.getData() == null) {
            return new ArrayList<>();
        }
        log.info("获取所有客户组别: {}", JSON.toJSONString(responseStr.getData()));
        List<OrderCustomerGroupVO> customerGroupList = JSON.parseObject(responseStr.getData(), new TypeReference<List<OrderCustomerGroupVO>>() {
        });
        // 过滤有效的
        return customerGroupList.stream().filter(item -> item.getLocked().equals((byte) 0)).collect(Collectors.toList());
    }


    /**
     * 校验数据
     *
     * @param emailRuleConfig 入参
     */
    private void verifyData(EmailRuleConfig emailRuleConfig) {
        // 校验场景id
        Pattern regex = Pattern.compile(PATTERN);
        Matcher matcher = regex.matcher(emailRuleConfig.getSceneId());
        if (!matcher.matches()) {
            throw new CommonException(MessageAccessor.getMessage(ErrorCodeConstant.ERROR_WRONG_DATA_FORMAT, LanguageHelper.locale()).desc());
        }
        emailRuleConfig.setSceneId(emailRuleConfig.getSceneId().toUpperCase());
        // 获取组别对应品牌
        CompletableFuture<OrderCustomerGroupVO> customerGroupBrandFuture = CompletableFuture.supplyAsync(() -> getCustomerGroupBrand(emailRuleConfig.getGroupId()));
        CompletableFuture<List<LovValueDTO>> lovValueListFuture = CompletableFuture.supplyAsync(() -> lovAdapter.queryLovValue(DJICM_BRAND, 0L));
        MessageTemplate messageTemplate = getCheckMessageTemplate(emailRuleConfig.getTemplateCode());
        // 校验发件邮箱
        EmailServer email = getAndCheckEmailServer(emailRuleConfig.getSendMailBox());
        // 唯一性规则：场景id+组别id+语种
        List<EmailRuleConfig> emailRuleConfigs = emailRuleConfigRepository.selectByCondition(Condition.builder(EmailRuleConfig.class).andWhere(Sqls.custom()
                .andEqualTo(EmailRuleConfig.FIELD_SCENE_ID, emailRuleConfig.getSceneId())
                .andEqualTo(EmailRuleConfig.FIELD_GROUP_ID, emailRuleConfig.getGroupId())
                .andEqualTo(EmailRuleConfig.FIELD_LANGUAGE, emailRuleConfig.getLanguage())
                .andNotEqualTo(EmailRuleConfig.FIELD_RULE_CONFIG_ID, emailRuleConfig.getRuleConfigId(), Boolean.TRUE)
                .andEqualTo(EmailRuleConfig.FIELD_DELETED, Boolean.FALSE)).build());
        if (CollectionUtils.isNotEmpty(emailRuleConfigs)) {
            throw new CommonException(MessageAccessor.getMessage(ErrorCodeConstant.ERROR_UNIQUE_RULE_REPEATS, LanguageHelper.locale()).desc());
        }
        List<LovValueDTO> lovValueList = lovValueListFuture.join();
        OrderCustomerGroupVO customerGroup = customerGroupBrandFuture.join();
        if (Objects.isNull(customerGroup)) {
            throw new CommonException(MessageAccessor.getMessage(ErrorCodeConstant.ERROR_REQUIREMENT_PLAN_BUSINESS_GROUP_NOT_FOUND, LanguageHelper.locale()).desc());
        }
        // 品牌隔离风险检查
        checkBrandIsolationRisk(emailRuleConfig.getSignName(), customerGroup, lovValueList, messageTemplate);
        // 组装参数
        emailRuleConfig.setGroupName(customerGroup.getName());
        emailRuleConfig.setGroupEnName(customerGroup.getEnName());
        emailRuleConfig.setSendMailBoxName(email.getServerName());
        emailRuleConfig.setTemplateId(messageTemplate.getTemplateId());
        emailRuleConfig.setTemplateName(messageTemplate.getTemplateName());
    }

    private EmailServer getAndCheckEmailServer(String serverCode) {
        EmailServer emailServer = (new EmailServer()).setServerCode(serverCode).setTenantId(0L).setEnabledFlag(1);
        EmailServer email = emailServerRepository.selectOne(emailServer);
        if (Objects.isNull(email)) {
            throw new CommonException(MessageAccessor.getMessage(ErrorCodeConstant.ERROR_INVALID_SEND_EMAIL, LanguageHelper.locale()).desc());
        }
        return email;
    }

    private MessageTemplate getCheckMessageTemplate(String templateCode) {
        // 消息模板有效性检查
        MessageTemplate selectCondition = (new MessageTemplate()).setTenantId(0L).setTemplateCode(templateCode).setEnabledFlag(1);
        MessageTemplate messageTemplate = messageTemplateRepository.selectOne(selectCondition);
        if (Objects.isNull(messageTemplate)) {
            throw new CommonException(MessageAccessor.getMessage(ErrorCodeConstant.ERROR_INVALID_MESSAGE_TEMPLATE, LanguageHelper.locale()).desc());
        }
        return messageTemplate;
    }

    /**
     * 获取组别对应品牌
     *
     * @param groupId 组别
     * @return 品牌
     */
    private OrderCustomerGroupVO getCustomerGroupBrand(Long groupId) {
        List<OrderCustomerGroupVO> customerGroupVOList = queryCustomerGroupList();
        Map<Integer, OrderCustomerGroupVO> groupIdToBrandMap = customerGroupVOList.stream().collect(Collectors.toMap(OrderCustomerGroupVO::getId, Function.identity(), (v1, v2) -> v1));
        return groupIdToBrandMap.get(groupId.intValue());
    }

    /**
     * 品牌隔离风险检查
     *
     * @param signName        发件落款
     * @param customerGroup   客户组别
     * @param lovValueList    组别对应品牌
     * @param messageTemplate 模板
     */
    @Override
    public void checkBrandIsolationRisk(String signName, OrderCustomerGroupVO customerGroup, List<LovValueDTO> lovValueList, MessageTemplate messageTemplate) {
        // 品牌隔离风险检查
        List<String> descriptionList = lovValueList.stream().filter(item -> !item.getValue().equalsIgnoreCase(customerGroup.getBrand()))
                .map(LovValueDTO::getDescription).collect(Collectors.toList());
        List<String> keywords = new ArrayList<>();
        // 模板内容过滤占位符
        String templateContent = StringUtils.isNotBlank(messageTemplate.getTemplateContent()) ? messageTemplate.getTemplateContent() : "";
        descriptionList.stream().filter(StringUtils::isNotBlank).forEach(description ->
                keywords.addAll(Arrays.stream(description.split(BaseConstants.Symbol.SEMICOLON)).collect(Collectors.toList())));
        log.info("品牌隔离风险检查:{},发件落款:{},模板主题:{},模板内容:{}", JSON.toJSONString(keywords), signName,
                messageTemplate.getTemplateTitle(), messageTemplate.getTemplateContent());
        templateContent = templateContent.replaceAll(REGEX, "");
        for (String keyword : keywords) {
            if (StringUtils.isNotBlank(signName) && signName.toLowerCase().contains(keyword.toLowerCase())) {
                throw new CommonException(MessageAccessor.getMessage(ErrorCodeConstant.ERROR_THE_CONTENT_DOES_NOT_MATCH_THE_BRAND, LanguageHelper.locale()).desc());
            }
            if (templateContent.toLowerCase().contains(keyword.toLowerCase())) {
                throw new CommonException(MessageAccessor.getMessage(ErrorCodeConstant.ERROR_THE_CONTENT_DOES_NOT_MATCH_THE_BRAND, LanguageHelper.locale()).desc());
            }
            if (StringUtils.isNotBlank(messageTemplate.getTemplateTitle()) && messageTemplate.getTemplateTitle().toLowerCase().contains(keyword.toLowerCase())) {
                throw new CommonException(MessageAccessor.getMessage(ErrorCodeConstant.ERROR_THE_CONTENT_DOES_NOT_MATCH_THE_BRAND, LanguageHelper.locale()).desc());
            }
        }
    }


    @Override
    public Message sendMailGeneral(SendMailGeneralDTO sendMailGeneral) {
        Message message = null;
        try {
            CompletableFuture.runAsync(() -> recordCallResult(JSON.toJSONString(sendMailGeneral)));
            EmailRuleConfig build = EmailRuleConfig.builder().sceneId(sendMailGeneral.getSceneId()).groupId(sendMailGeneral.getGroupId())
                    .language(sendMailGeneral.getLanguage()).deleted(Boolean.FALSE).build();
            List<EmailRuleConfig> emailRuleConfigList = emailRuleConfigRepository.selectByCondition(Condition.builder(EmailRuleConfig.class).andWhere(
                            Sqls.custom().andEqualTo(EmailRuleConfig.FIELD_SCENE_ID, sendMailGeneral.getSceneId())
                                    .andEqualTo(EmailRuleConfig.FIELD_GROUP_ID, sendMailGeneral.getGroupId())
                                    .andEqualTo(EmailRuleConfig.FIELD_LANGUAGE, sendMailGeneral.getLanguage())
                                    .andEqualTo(EmailRuleConfig.FIELD_DELETED, Boolean.FALSE)).build());
            if (CollectionUtils.isEmpty(emailRuleConfigList)) {
                throw new CommonException(MessageAccessor.getMessage(ErrorCodeConstant.ERROR_NO_EMAIL_SEND_RULES_CONFIG, LanguageHelper.locale()).desc());
            }
            if (emailRuleConfigList.size() > 1) {
                throw new CommonException(MessageAccessor.getMessage(ErrorCodeConstant.ERROR_SIZE_EMAIL_SEND_RULES_CONFIG, LanguageHelper.locale()).desc());
            }
            MessageSender messageSender = sendMailGeneral.buildMessageSender(emailRuleConfigList.get(0));
            log.info("发送邮件参数:{}", JSON.toJSONString(messageSender));
            message = emailSendService.sendMessage(messageSender);
            log.info("发送邮件异常结果:{}", JSON.toJSONString(message));
            return message;
        } catch (Exception e) {
            log.error("发送邮件异常,场景id:" + sendMailGeneral.getSceneId(), e);
            // 发送GT消息
            CompletableFuture.runAsync(() -> sendGtMessage(sendMailGeneral.getSceneId(), e.getMessage()));
        }
        return message;
    }

    /**
     * 发送GT消息
     *
     * @param sceneId  场景id
     * @param errorMsg 错误信息
     */
    private void sendGtMessage(String sceneId, String errorMsg) {
        MessageSender messageSender = new MessageSender();
        messageSender.setServerCode(SERVER_CODE);
        messageSender.setMessageCode(TemplateConstants.MessageTemplateCode.O2OM_GATEWAY_FAILURE_GT);
        org.hzero.boot.message.entity.Message messageContent = new org.hzero.boot.message.entity.Message();
        messageContent.setContent("场景id:" + sceneId + ",发送邮件异常:" + errorMsg);
        messageSender.setMessage(messageContent);
        messageSender.setLang(LanguageHelper.getDefaultLanguage());
        messageSender.setTenantId(0L);
        messageSender.setGroupCode(TemplateConstants.MessageTemplateCode.O2OM_GATEWAY_FAILURE_GT);
        log.info("发送邮件异常异常 request：{}", JSON.toJSONString(messageSender));
        Message message = sendService.sendMessage(messageSender);
        log.info("发送邮件异常异常 response：{}", JSON.toJSONString(message));
    }

    /**
     * 记录接口的调用记录
     *
     * @param reqBody 接口请求Body参数
     */
    private void recordCallResult(String reqBody) {
        DjiInterfaceLogDTO interfaceLogDTO = new DjiInterfaceLogDTO();
        DjiInterfaceLogDtlDTO interfaceLogDtlDTO = new DjiInterfaceLogDtlDTO();
        interfaceLogDTO.setApplicationCode(SYSTEM_CODE);
        interfaceLogDTO.setApplicationName(SYSTEM_CODE);
        interfaceLogDTO.setInterfaceCode("sendMailGeneral");
        interfaceLogDTO.setInterfaceName("发送邮件通用接口");
        interfaceLogDTO.setServerCode(SYSTEM_CODE);
        interfaceLogDTO.setServerName(SYSTEM_NAME);
        interfaceLogDTO.setInterfaceType("POST");
        interfaceLogDTO.setInvokeKey("发送邮件通用接口" + "-" + System.currentTimeMillis());
        interfaceLogDTO.setInterfaceUrl("sendMailGeneral");
        interfaceLogDTO.setRequestTime(new Date());
        interfaceLogDTO.setTenantId(0L);
        interfaceLogDTO.setResponseStatus("success");
        interfaceLogDtlDTO.setInvokeKey(String.valueOf(System.currentTimeMillis()));
        interfaceLogDtlDTO.setInterfaceReqBodyParam(reqBody);
        interfaceLogDtlDTO.setInterfaceRespContent("");
        interfaceLogDtlDTO.setReqBodyParam(reqBody);
        interfaceLogDtlDTO.setRespContent(reqBody);
        interfaceLogDtlDTO.setTenantId(0L);
        interfaceLogDTO.setInterfaceLogDtlDTO(interfaceLogDtlDTO);
        djiInterfaceFeign.createLog(0L, interfaceLogDTO);
    }

    @Override
    public List<EmailConfigTemplateInfoVO> getEmailRuleAndTemplateInfo(List<EmailRuleConfigCmQueryDTO> emailRuleConfigQueryDTOList) {
        return emailRuleConfigRepository.queryEmailRuleConfigCmList(emailRuleConfigQueryDTOList);
    }
}
