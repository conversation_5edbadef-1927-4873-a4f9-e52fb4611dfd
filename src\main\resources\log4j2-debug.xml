<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2020. DJI All Rights Reserved.
  ~
  ~ 开发期调试用
  -->
<configuration status="off" monitorInterval="1800">
	<appenders>
		<Console name="STDOUT" target="SYSTEM_OUT" follow="true">
			<PatternLayout pattern="[%d{yyyy-MM-dd HH:mm:ss} %5p [%t] %c{1.}:%L] %m%n" />
		</Console>
	</appenders>
	<loggers>
		<!-- 根据自己的需要添加logger & appender -->
		<!-- o2 log -->
<!--		<logger name="org.o2" level="${sys:LOG_LEVEL:debug}">-->
<!--			<appender-ref ref="o2" />-->
<!--		</logger>-->
		<!-- other framework -->

		<logger name="org.springframework" level="info" />
		<logger name="org.mybatis" level="debug" />
		<logger name="com.netflix.discovery" level="info" />
		<logger name="io.choerodon" level="debug" />
		<logger name="org.hzero" level="debug"/>
		<logger name="com.dji" level="debug"/>
		<!--<logger name="org.o2" level="debug" />-->
		<!--<logger name="com.dji" level="debug" />-->
<!--		<root level="debug">-->
<!--			<appender-ref ref="STDOUT" />-->
<!--		</root>-->
	</loggers>
</configuration>
