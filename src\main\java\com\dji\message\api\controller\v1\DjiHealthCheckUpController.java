package com.dji.message.api.controller.v1;


import io.choerodon.swagger.annotation.Permission;
import org.apache.poi.ss.formula.functions.T;
import org.hzero.core.util.Results;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 大疆健康检查 Controller
 *
 * <AUTHOR> 2020/12/16 11:25
 **/
@RestController("djiHealthCheckUpController.v1")
@RequestMapping("/v1/dji-health")
public class DjiHealthCheckUpController {

    @GetMapping
    @Permission(permissionPublic = true)
    public ResponseEntity<T> check() {
        return Results.success();
    }

}
