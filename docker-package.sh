#!/bin/bash -e
mkdir -p /kaniko/.docker

echo "kaniko execute start"

echo "{\"auths\":{\"$CI_REGISTRY\":{\"username\":\"$CI_REGISTRY_USER\",\"password\":\"$CI_REGISTRY_PASSWORD\"}}}" >/kaniko/.docker/config.json
/kaniko/executor \
  --context $CI_PROJECT_DIR \
  --dockerfile $CI_PROJECT_DIR/Dockerfile \
  --destination $IMAGE_HOST/$REGISTRY_PROJECT/$PAMS_APPLICATION_EN_NAME:$CI_PIPELINE_ID \
  --build-arg IMAGE_HOST=$IMAGE_HOST --build-arg JAR_FILE=$JAR_FILE 2>&1

echo "kaniko execute end"