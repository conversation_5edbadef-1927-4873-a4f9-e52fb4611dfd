package org.hzero.boot.message.entity;

import com.google.common.base.Objects;
import io.swagger.annotations.ApiModelProperty;
import org.hzero.core.base.BaseConstants;

import javax.validation.constraints.NotBlank;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * description 重写MessageSender方法增加用户组字段
 *
 * <AUTHOR> 2021/03/18 15:17
 */
public class MessageSender extends BaseSender {
	private String receiveConfigCode;
	@NotBlank(
			groups = {MessageSender.Sms.class, MessageSender.Email.class}
	)
	private String serverCode;
	private String receiverTypeCode;
	private List<Receiver> receiverAddressList;
	private List<String> typeCodeList;
	private Map<String, String> args;
	private Map<String, Object> objectArgs;
	private Message message;
	private Map<String, Message> messageMap;
	private List<Attachment> attachmentList;
	private List<String> ccList;
	private List<String> bccList;
	private Integer batchSend;
	@ApiModelProperty(value = "要发送的群ID，通过UMS管理后台的自助服务可以查询")
	private List<String> roomIdList;
	@ApiModelProperty(value = "GT消息模板id")
	private Integer templateId;
	@ApiModelProperty(value = "用户组编码")
	private String groupCode;
	@ApiModelProperty(value = "发件人,通常是公共帐号，不指定的情况下默认为ums-msg，即ums<EMAIL>,")
	private String sender;
	public MessageSender() {
		this.batchSend = BaseConstants.Flag.YES;
	}
	public MessageSender(MessageSender sender) {
		this.batchSend = BaseConstants.Flag.YES;
		this.tenantId = sender.getTenantId();
		this.messageCode = sender.getMessageCode();
		this.lang = sender.getLang();
		this.additionalInformation = sender.getAdditionalInformation();
		this.receiveConfigCode = sender.getReceiveConfigCode();
		this.serverCode = sender.getServerCode();
		this.receiverTypeCode = sender.getReceiverTypeCode();
		ArrayList attachments;
		if (sender.getReceiverAddressList() != null) {
			attachments = new ArrayList();
			ArrayList finalAttachments = attachments;
			sender.getReceiverAddressList().forEach((item) -> {
				finalAttachments.add(new Receiver(item));
			});
			this.receiverAddressList = attachments;
		} else {
			this.receiverAddressList = null;
		}

		this.typeCodeList = sender.typeCodeList;
		this.args = sender.getArgs();
		this.objectArgs = sender.getObjectArgs();
		this.message = sender.getMessage() == null ? null : new Message(sender.getMessage());
		if (sender.getMessageMap() != null) {
			Map<String, Message> map = new HashMap(16);
			sender.getMessageMap().forEach((k, v) -> {
				Message var10000 = (Message)map.put(k, new Message(v));
			});
			this.messageMap = map;
		} else {
			this.messageMap = null;
		}

		if (sender.getAttachmentList() != null) {
			attachments = new ArrayList();
			ArrayList finalAttachments1 = attachments;
			sender.getAttachmentList().forEach((item) -> {
				finalAttachments1.add(new Attachment(item));
			});
			this.attachmentList = attachments;
		} else {
			this.attachmentList = null;
		}

		this.ccList = sender.getCcList();
		this.bccList = sender.getBccList();
		this.batchSend = sender.getBatchSend();
	}

	@Override
	public MessageSender setTenantId(Long tenantId) {
		this.tenantId = tenantId;
		return this;
	}

	@Override
	public MessageSender setMessageCode(String messageCode) {
		this.messageCode = messageCode;
		return this;
	}

	@Override
	public MessageSender setLang(String lang) {
		this.lang = lang;
		return this;
	}

	public String getReceiveConfigCode() {
		return this.receiveConfigCode;
	}

	public MessageSender setReceiveConfigCode(String receiveConfigCode) {
		this.receiveConfigCode = receiveConfigCode;
		return this;
	}

	public String getServerCode() {
		return this.serverCode;
	}

	public MessageSender setServerCode(String serverCode) {
		this.serverCode = serverCode;
		return this;
	}

	public String getReceiverTypeCode() {
		return this.receiverTypeCode;
	}

	public MessageSender setReceiverTypeCode(String receiverTypeCode) {
		this.receiverTypeCode = receiverTypeCode;
		return this;
	}

	public List<Receiver> getReceiverAddressList() {
		return this.receiverAddressList;
	}

	public MessageSender setReceiverAddressList(List<Receiver> receiverAddressList) {
		this.receiverAddressList = receiverAddressList;
		return this;
	}

	public List<String> getTypeCodeList() {
		return this.typeCodeList;
	}

	public MessageSender setTypeCodeList(List<String> typeCodeList) {
		this.typeCodeList = typeCodeList;
		return this;
	}

	public Map<String, String> getArgs() {
		return this.args;
	}

	public MessageSender setArgs(Map<String, String> args) {
		this.args = args;
		return this;
	}

	public Map<String, Object> getObjectArgs() {
		return this.objectArgs;
	}

	public MessageSender setObjectArgs(Map<String, Object> objectArgs) {
		this.objectArgs = objectArgs;
		return this;
	}

	public Message getMessage() {
		return this.message;
	}

	public MessageSender setMessage(Message message) {
		this.message = message;
		return this;
	}

	public Map<String, Message> getMessageMap() {
		return this.messageMap;
	}

	public MessageSender setMessageMap(Map<String, Message> messageMap) {
		this.messageMap = messageMap;
		return this;
	}

	public List<Attachment> getAttachmentList() {
		return this.attachmentList;
	}

	public MessageSender setAttachmentList(List<Attachment> attachmentList) {
		this.attachmentList = attachmentList;
		return this;
	}

	public List<String> getCcList() {
		return this.ccList;
	}

	public MessageSender setCcList(List<String> ccList) {
		this.ccList = ccList;
		return this;
	}

	public List<String> getBccList() {
		return this.bccList;
	}

	public MessageSender setBccList(List<String> bccList) {
		this.bccList = bccList;
		return this;
	}

	public Integer getBatchSend() {
		return this.batchSend;
	}

	public MessageSender setBatchSend(Integer batchSend) {
		this.batchSend = batchSend;
		return this;
	}

	@Override
	public boolean equals(Object o) {
		if (this == o) {
			return true;
		} else if (!(o instanceof MessageSender)) {
			return false;
		} else {
			MessageSender that = (MessageSender)o;
			return Objects.equal(this.tenantId, that.tenantId) && Objects.equal(this.messageCode, that.messageCode) && Objects.equal(this.serverCode, that.serverCode) && Objects.equal(this.lang, that.lang) && Objects.equal(this.receiverTypeCode, that.receiverTypeCode) && Objects.equal(this.receiverAddressList, that.receiverAddressList) && Objects.equal(this.typeCodeList, that.typeCodeList) && Objects.equal(this.args, that.args) && Objects.equal(this.objectArgs, that.objectArgs) && Objects.equal(this.message, that.getMessage()) && Objects.equal(this.messageMap, that.getMessageMap()) && Objects.equal(this.attachmentList, that.attachmentList) && Objects.equal(this.ccList, that.ccList) && Objects.equal(this.bccList, that.bccList) && Objects.equal(this.batchSend, that.batchSend);
		}
	}

	@Override
	public int hashCode() {
		return Objects.hashCode(new Object[]{this.tenantId, this.messageCode, this.serverCode, this.lang, this.receiverTypeCode, this.receiverAddressList, this.typeCodeList, this.args, this.objectArgs, this.message, this.messageMap, this.attachmentList, this.ccList, this.bccList, this.batchSend});
	}

	public static MessageSender.MessageSenderBuilder builder() {
		return new MessageSender.MessageSenderBuilder(new MessageSender());
	}

	@Override
	public String toString() {
		return "MessageSender{tenantId=" + this.tenantId + ", messageCode='" + this.messageCode + '\'' + ", receiveConfigCode='" + this.receiveConfigCode + '\'' + ", serverCode='" + this.serverCode + '\'' + ", lang='" + this.lang + '\'' + ", receiverTypeCode='" + this.receiverTypeCode + '\'' + ", receiverAddressList=" + this.receiverAddressList + ", typeCodeList=" + this.typeCodeList + ", args=" + this.args + ", objectArgs=" + this.objectArgs + ", message=" + this.message + ", messageMap=" + this.messageMap + ", attachmentList=" + this.attachmentList + ", ccList=" + this.ccList + ", bccList=" + this.bccList + ", batchSend=" + this.batchSend + '}';
	}

	public interface Call {
	}

	public interface Email {
	}

	public interface Sms {
	}

	public interface WebMessage {
	}

	public static class MessageSenderBuilder {
		private MessageSender messageSender;

		public MessageSenderBuilder(MessageSender messageSender) {
			this.messageSender = messageSender;
		}

		public MessageSender.MessageSenderBuilder tenantId(long tenantId) {
			this.messageSender.setTenantId(tenantId);
			return this;
		}

		public MessageSender.MessageSenderBuilder messageCode(String messageCode) {
			this.messageSender.setMessageCode(messageCode);
			return this;
		}

		public MessageSender.MessageSenderBuilder receiveConfigCode(String receiveConfigCode) {
			this.messageSender.setReceiveConfigCode(receiveConfigCode);
			return this;
		}

		public MessageSender.MessageSenderBuilder serverCode(String serverCode) {
			this.messageSender.setServerCode(serverCode);
			return this;
		}

		public MessageSender.MessageSenderBuilder lang(String lang) {
			this.messageSender.setLang(lang);
			return this;
		}

		public MessageSender.MessageSenderBuilder receiverTypeCode(String receiverTypeCode) {
			this.messageSender.setReceiverTypeCode(receiverTypeCode);
			return this;
		}

		public MessageSender.MessageSenderBuilder receiverAddressList(List<Receiver> receiverAddressList) {
			this.messageSender.setReceiverAddressList(receiverAddressList);
			return this;
		}

		public MessageSender.MessageSenderBuilder typeCodeList(List<String> typeCodeList) {
			this.messageSender.setTypeCodeList(typeCodeList);
			return this;
		}

		public MessageSender.MessageSenderBuilder args(Map<String, String> args) {
			this.messageSender.setArgs(args);
			return this;
		}

		public MessageSender.MessageSenderBuilder objectArgs(Map<String, Object> objectArgs) {
			this.messageSender.setObjectArgs(objectArgs);
			return this;
		}

		public MessageSender.MessageSenderBuilder addArg(String argName, String argValue) {
			if (this.messageSender.getArgs() == null) {
				this.messageSender.setArgs(new HashMap(16));
			}

			this.messageSender.getArgs().put(argName, argValue);
			return this;
		}

		public MessageSender.MessageSenderBuilder attachmentList(List<Attachment> attachmentList) {
			this.messageSender.setAttachmentList(attachmentList);
			return this;
		}

		public MessageSender.MessageSenderBuilder ccList(List<String> ccList) {
			this.messageSender.setCcList(ccList);
			return this;
		}

		public MessageSender.MessageSenderBuilder bccList(List<String> bccList) {
			this.messageSender.setBccList(bccList);
			return this;
		}

		public MessageSender.MessageSenderBuilder batchSend(Integer batchSend) {
			this.messageSender.setBatchSend(batchSend);
			return this;
		}

		public MessageSender build() {
			if (this.messageSender.getTenantId() == null) {
				this.messageSender.setTenantId(BaseConstants.DEFAULT_TENANT_ID);
			}

			return this.messageSender;
		}
	}
	public String getGroupCode() {
		return groupCode;
	}

	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}

	public String getSender() {
		return sender;
	}

	public void setSender(String sender) {
		this.sender = sender;
	}

	public List<String> getRoomIdList() {
		return roomIdList;
	}

	public void setRoomIdList(List<String> roomIdList) {
		this.roomIdList = roomIdList;
	}

	public Integer getTemplateId() {
		return templateId;
	}

	public void setTemplateId(Integer templateId) {
		this.templateId = templateId;
	}
}
