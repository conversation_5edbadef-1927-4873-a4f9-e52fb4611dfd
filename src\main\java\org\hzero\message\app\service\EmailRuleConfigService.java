package org.hzero.message.app.service;

import io.choerodon.core.domain.*;
import io.choerodon.mybatis.pagehelper.domain.*;
import org.hzero.boot.platform.lov.dto.*;
import org.hzero.message.api.dto.*;
import org.hzero.message.domain.entity.*;
import org.hzero.message.domain.vo.*;

import java.util.*;

/**
 * 邮件规则配置表应用服务
 *
 * <AUTHOR> 2024-06-14 15:38:08
 */
public interface EmailRuleConfigService {

    /**
     * 获取邮件规则配置列表
     * @param pageRequest 分页参数
     * @param queryDTO 查询条件
     * @return 结果
     */
    Page<EmailRuleConfigResVO> queryEmailRuleConfigListPage(PageRequest pageRequest, EmailRuleConfigQueryDTO queryDTO);

    /**
     * 获取邮件规则配置详情
     * @param ruleConfigId id
     * @return 结果
     */
    EmailRuleConfigResVO findEmailRuleConfigDetail(String ruleConfigId);

    /**
     * 新建邮件规则配置
     * @param saveDTO 新建参数
     * @return 结果
     */
    String create(EmailRuleConfigSaveDTO saveDTO);

    /**
     * 更新邮件规则配置
     * @param updateDTO 更新参数
     * @return 结果
     */
    String update(EmailRuleConfigUpdateDTO updateDTO);

    /**
     * 删除邮件规则配置
     * @param ruleConfigId 规则配置id
     * @return 结果
     */
    Boolean remove(String ruleConfigId);

    /**
     * 导出邮件规则配置
     * @param queryDTO 入参
     * @return 结果
     */
    List<EmailRuleConfigExportDTO> export(EmailRuleConfigQueryDTO queryDTO);

    /**
     * 获取所有客户组别
     * @return 客户组别
     */
    List<OrderCustomerGroupVO> queryCustomerGroupList();

    /**
     * 品牌隔离风险检查
     * @param signName 发件落款
     * @param customerGroup 品牌
     * @param lovValueList 组别对应品牌
     * @param messageTemplate 模板
     */
    void checkBrandIsolationRisk(String signName, OrderCustomerGroupVO customerGroup, List<LovValueDTO> lovValueList, MessageTemplate messageTemplate);

    /**
     * 发送邮件通用接口
     * @param sendMailGeneral 入参
     * @return 结果
     */
    Message sendMailGeneral(SendMailGeneralDTO sendMailGeneral);

    /**
     * 通过查询条件获取邮箱发送规则配置及配置模版具体内容
     * @param emailRuleConfigQueryDTOList
     * @return
     */
    List<EmailConfigTemplateInfoVO> getEmailRuleAndTemplateInfo(List<EmailRuleConfigCmQueryDTO> emailRuleConfigQueryDTOList);
}
