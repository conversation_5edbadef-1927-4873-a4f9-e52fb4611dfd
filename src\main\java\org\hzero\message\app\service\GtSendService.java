package org.hzero.message.app.service;

import org.hzero.boot.message.entity.*;
import org.hzero.message.api.dto.*;
import org.hzero.message.domain.entity.Message;

/**
 * gt消息发送
 *
 * <AUTHOR> 2020/10/14 17:31
 */
public interface GtSendService {
    /**
     * 发送消息
     *
     * @param messageSender 消息推送内容
     * @return org.hzero.message.domain.entity.Message
     */
    Message sendMessage(MessageSender messageSender);

    /**
     * 重试发送
     * @param message 用户消息信息
     * @return org.hzero.message.domain.entity.Message
     */
    Message resendMessage(UserMessageDTO message);

    /**
     * GT消息发群消息
     * @param messageSender GT发送参数
     * @return 结果
     */
    Message sendGtGroupMessage(MessageSender messageSender);
}
