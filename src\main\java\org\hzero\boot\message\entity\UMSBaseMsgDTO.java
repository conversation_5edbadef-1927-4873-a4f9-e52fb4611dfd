package org.hzero.boot.message.entity;

import io.swagger.annotations.*;
import lombok.*;
import lombok.experimental.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel(value = "ums电话报警参数")
public class UMSBaseMsgDTO {

    @ApiModelProperty(value = "必填，你的应用 Pams ID ")
    String appId;

    @ApiModelProperty(value = "必填，业务ID，推荐唯一，方便排查问题 ")
    String bizId;

    @ApiModelProperty(value = "必填，UMS语音模板ID")
    String templateId;


    @ApiModelProperty(value = "手机号码,与AD参数二选一必填一项，两者都填只生效该项")
    String phoneNumber;


    @ApiModelProperty(value = "用户AD,与phoneNumber二选一必填一项")
    String ad;

    @ApiModelProperty(value = "非必填，一通电话内语音通知内容的播放次数，取值范围为1~3")
    Integer playTimes;

    @ApiModelProperty(value = "非必填，语速控制。取值范围为-500~500")
    Integer speed;

    @ApiModelProperty(value = "模板参数字典，会把对应模板中形如 ${xxx} 格式的内容做替换")
    ParametersDTO parameters;

    @Data
    @FieldDefaults(level = AccessLevel.PRIVATE)
    private static class ParametersDTO {

        @ApiModelProperty(value = "模板参数字典，会把对应模板中形如 ${xxx} 格式的内容做替换")
        String sys;
    }
}
