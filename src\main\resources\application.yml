spring:
  application:
    name: hzero-message
    hone-route:
      name: hmes
      path: /hmes/**
  datasource:
    url: ${SPRING_DATASOURCE_URL:*************************************************************************************************}
    username: ${SPRING_DATASOURCE_USERNAME:hzero}
    password: ${SPRING_DATASOURCE_PASSWORD:hzero}
    hikari:
      # 连接池最小空闲连接数
      minimum-idle: ${SPRING_DATASOURCE_MINIMUM_IDLE:20}
      # 连接池允许的最大连接数
      maximum-pool-size: ${SPRING_DATASOURCE_MAXIMUM_POOL_SIZE:200}
      # 等待连接池分配连接的最大时长（毫秒）
      connection-timeout: ${SPRING_DATASOURCE_CONNECTION_TIMEOUT:30000}
  redis:
    host: ${SPRING_REDIS_HOST:r-wz9cu6xghvzcc1aqly.redis.rds.aliyuncs.com}
    port: ${SPRING_REDIS_PORT:6379}
    database: ${SPRING_REDIS_DATABASE:1}
    password: ${SPRING_REDIS_PASSWORD:xxx}
    jedis:
      pool:
        # 资源池中最大连接数
        # 默认8，-1表示无限制；可根据服务并发redis情况及服务端的支持上限调整
        max-active: ${SPRING_REDIS_POOL_MAX_ACTIVE:50}
        # 资源池运行最大空闲的连接数
        # 默认8，-1表示无限制；可根据服务并发redis情况及服务端的支持上限调整，一般建议和max-active保持一致，避免资源伸缩带来的开销
        max-idle: ${SPRING_REDIS_POOL_MAX_IDLE:50}
        # 当资源池连接用尽后，调用者的最大等待时间(单位为毫秒)
        # 默认 -1 表示永不超时，设置5秒
        max-wait: ${SPRING_REDIS_POOL_MAX_WAIT:5000}

feign:
  hystrix:
    enabled: true

hystrix:
  threadpool:
    default:
      # 执行命令线程池的核心线程数，也是命令执行的最大并发量
      # 默认10
      coreSize: 1000
      # 最大执行线程数
      maximumSize: 1000
  command:
    default:
      execution:
        isolation:
          thread:
            # HystrixCommand 执行的超时时间，超时后进入降级处理逻辑。一个接口，理论的最佳响应速度应该在200ms以内，或者慢点的接口就几百毫秒。
            # 默认 1000 毫秒，最高设置 2000足矣。如果超时，首先看能不能优化接口相关业务、SQL查询等，不要盲目加大超时时间，否则会导致线程堆积过多，hystrix 线程池卡死，最终服务不可用。
            timeoutInMilliseconds: ${HYSTRIX_COMMAND_TIMEOUT_IN_MILLISECONDS:40000}
  devops-deploy-service:
    execution:
      isolation:
        thread:
          timeoutInMilliseconds: 5000

ribbon:
  # 客户端读取超时时间，超时时间要小于Hystrix的超时时间，否则重试机制就无意义了
  ReadTimeout: ${RIBBON_READ_TIMEOUT:30000}
  # 客户端连接超时时间
  ConnectTimeout: ${RIBBON_CONNECT_TIMEOUT:3000}
  # 访问实例失败(超时)，允许自动重试，设置重试次数，失败后会更换实例访问，请一定确保接口的幂等性，否则重试可能导致数据异常。
  OkToRetryOnAllOperations: true
  MaxAutoRetries: 1
  MaxAutoRetriesNextServer: 1
  # 实例列表缓存时间，设置不大于3θs（k8s生命周期 preStop的默认时间)
  ServerListRefreshInterlval: ${RIBBON_SERVER_LIST_REFRESH_INTERVAL:10000}

mybatis:
  mapperLocations: classpath*:/mapper/*.xml
  configuration:
    mapUnderscoreToCamelCase: true
mapper:
  not-empty: true

# actuator 禁用heapdump env端点
management:
  endpoint:
    heapdump:
      enabled: false
    env:
      enabled: true

hzero:
  lov:
    sql:
      enabled: true
    value:
      enabled: true
  message:
    message-redis-database: 1
    sms:
      fake-action: ${HZERO_SMS_FAKE:false}
  data:
    permission:
      db-owner: ${HZERO_DB_OWNER:}
  export:
    enable-async: true
  import:
    init-table: true #自动建表
    initTable: true
    batch-size: 10000 #批次量
  scheduler:
    executor-code: ${HZERO_SCHEDULER_CODE:HMSG_EXECUTOR}
    auto-register: true
    upload-log: true
  cache-value:
    enable: true
  resource:
    # 匹配的资源才会解析JwtToken便于得到UserDetails
    pattern: ${HZERO_RESOURCE_PATTERN:/v1/*,/hmsg/v1/*}
    # 配置请求头
    auth-header-name: ${AUTH_HEADER_NAME:x_prm_authorization}

#  service:
#    admin:
#      name: o2-admin

logging:
  config: classpath:log4j2.xml

message:
  gt:
    appId: ${MESSAGE_APPID:o2-message}
    url: ${MESSAGE_URL:https://gw-sanbox.djicorp.com/api}
    gwId: ${MESSAGE_GWID:o2-basic-message-be-dev-aliyun-sz}
    gwSecret: ${MESSAGE_GWSECRET:XXX}
    testMailbox: ${TEST_MAILBOX:true}
    testMail: ${TEST_MAIL:<EMAIL>,<EMAIL>}
    smsUrl: ${smsUrl:dji-ums.send.sms_v2.post}

# GT 注册的应用 appId appKey
gt:
  appId: ${GT_APP_ID:617025}
  appKey: ${GT_APP_KEY:cK574lBJy6XKG3PMzolHw6WSDtCKVwWY}
  # GT 注册的应用绑定的服务号
  account: ${GT_ACCOUNT:gttest1004}
  # 调用的GT环境 test 测试、prod 正式
  env: ${GT_ENV:test}

dava:
  gw:
    serverUrl: ${DAVA_GW_SERVER_URL:https://gw-sanbox.djicorp.com/api}
    appId: ${DAVA_GW_APP_ID:o2-basic-message-be-test-aliyun-sz}
    appSecret: ${DAVA_GW_APP_SECRET:}
