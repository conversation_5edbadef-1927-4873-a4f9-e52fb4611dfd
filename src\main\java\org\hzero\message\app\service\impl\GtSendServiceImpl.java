package org.hzero.message.app.service.impl;

import com.alibaba.fastjson.*;
import com.dji.dava.gateway.httpclient.*;
import com.dji.gt.sdk.*;
import io.choerodon.core.exception.*;
import org.hzero.boot.message.entity.*;
import org.hzero.core.base.*;
import org.hzero.message.api.dto.*;
import org.hzero.message.app.service.*;
import org.hzero.message.config.*;
import org.hzero.message.constant.*;
import org.hzero.message.domain.entity.Message;
import org.hzero.message.domain.entity.*;
import org.hzero.message.domain.repository.*;
import org.hzero.message.domain.vo.*;
import org.hzero.message.infra.mapper.*;
import org.slf4j.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.stereotype.*;
import org.springframework.transaction.interceptor.*;
import org.springframework.util.*;

import java.util.*;
import java.util.stream.*;

/**
 * @Author: c-halk.liu
 * @Description:gt消息发送
 * @Date: 2020/10/14 17:30
 */
@Service
public class GtSendServiceImpl extends AbstractSendService implements GtSendService {

    private Logger logger = LoggerFactory.getLogger(WebSendServiceImpl.class);

    @Autowired
    private GtClient gtClient;

    @Autowired
    private MessageConfig messageConfig;

    @Autowired
    private MessageReceiverService messageReceiverService;

    @Autowired
    private MessageGeneratorService messageGeneratorService;

    @Autowired
    private MessageRepository messageRepository;

    @Autowired
    private MessageReceiverRepository messageReceiverRepository;

    @Autowired
    private MessageTransactionRepository messageTransactionRepository;

    @Autowired
    private DjiUserGroupMapper djiUserGroupMapper;

    private static final int SUCCESS_CODE = 200;

    private static final String RESULT_CODE = "resultCode";

    @Override
    public Message sendMessage(MessageSender messageSender) {
        messageSender = this.messageReceiverService.queryReceiver(messageSender);
        //存在接受者没有用户id得情况，该类情况不需要设置多语言，用默认得方式即可
        Message message = this.createMessage(messageSender, "GT");
        message = this.messageGeneratorService.generateMessage(messageSender, message);
        this.messageRepository.updateByPrimaryKeySelective(message);
        List<String> receiverAddressList = new ArrayList<>();
        //判断用户组编码是否为空，不为空走用户组
        //为空走默认传的发发件人
        if (StringUtils.hasText(messageSender.getGroupCode())) {
            List<Receiver> list = djiUserGroupMapper.selectUserGroupList(messageSender.getGroupCode());
            messageSender.setReceiverAddressList(list);
        }
        List<Receiver> receiverList = messageSender.getReceiverAddressList();
        Message finalMessage = message;
        receiverList.forEach((receiver) -> {
            receiverAddressList.add(receiver.getEmail());
            this.messageReceiverRepository.insertSelective((new MessageReceiver()).setMessageId(finalMessage.getMessageId()).setTenantId(finalMessage.getTenantId()).setReceiverAddress(receiver.getEmail()));
        });
        try {
            this.sendHttpToGt(receiverAddressList, message);
            this.messageRepository.updateByPrimaryKeySelective(message.setSendFlag(BaseConstants.Flag.YES));
            this.messageTransactionRepository.insertSelective((new MessageTransaction()).setMessageId(message.getMessageId()).setTrxStatusCode("S").setTenantId(message.getTenantId()));
            return message;
        } catch (Exception e) {
            logger.error("Send gt failed [{} -> {}] : {}", new Object[]{message.getServerCode(), message.getMessageReceiverList(), e.fillInStackTrace()});
            this.messageTransactionRepository.insertSelective((new MessageTransaction()).setMessageId(message.getMessageId()).setTrxStatusCode("F").setTenantId(message.getTenantId()).setTransactionMessage(e.getMessage()));
            //手动回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return message;
        }
    }

    @Override
    public Message resendMessage(UserMessageDTO message) {
        if (CollectionUtils.isEmpty(message.getMessageReceiverList())) {
            return message;
        } else {
            try {
                this.sendHttpToGt(message.getMessageReceiverList().stream().map(MessageReceiver::getReceiverAddress).collect(Collectors.toList()), message);
                this.successProcessUpdate(message);
            } catch (Exception e) {
                logger.error("Send gt failed [{} -> {}] : {}", new Object[]{message.getServerCode(), message.getMessageReceiverList(), e.fillInStackTrace()});
                this.failedProcessUpdate(message, e);
            }
        }
        return message;
    }

    @Override
    public Message sendGtGroupMessage(MessageSender messageSender) {
        if (CollectionUtils.isEmpty(messageSender.getRoomIdList())) {
            throw new CommonException("roomId Can not be empty");
        }
        logger.info("sendGtGroupMessage 参数:{}", JSON.toJSONString(messageSender));
        messageSender = this.messageReceiverService.queryReceiver(messageSender);
        //存在接受者没有用户id得情况，该类情况不需要设置多语言，用默认得方式即可
        Message message = this.createMessage(messageSender, "GT");
        message = this.messageGeneratorService.generateMessage(messageSender, message);
        this.messageRepository.updateByPrimaryKeySelective(message);
        try {
            logger.info("sendGtGroup Message params:{}", JSON.toJSONString(message));
            // 发GT消息到群
            this.sendGtGroup(messageSender.getRoomIdList(), messageSender.getTemplateId(), message);
            this.messageRepository.updateByPrimaryKeySelective(message.setSendFlag(BaseConstants.Flag.YES));
            this.messageTransactionRepository.insertSelective((new MessageTransaction()).setMessageId(message.getMessageId()).setTrxStatusCode("S").setTenantId(message.getTenantId()));
            return message;
        } catch (Exception e) {
            logger.error("Send gt failed [{} -> {}] : {}", new Object[]{message.getServerCode(), message.getMessageReceiverList(), e.fillInStackTrace()});
            this.messageTransactionRepository.insertSelective((new MessageTransaction()).setMessageId(message.getMessageId()).setTrxStatusCode("F").setTenantId(message.getTenantId()).setTransactionMessage(e.getMessage()));
            //手动回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return message;
        }
    }

    /**
     * 发送Gt群消息
     * @param roomIdList 群id
     * @param templateId 消息模板id
     * @param message 消息
     */
    private void sendGtGroup(List<String> roomIdList, Integer templateId, Message message) {
        List<com.dji.gt.sdk.dto.Receiver> receiverList = roomIdList.stream().map(roomId -> {
            com.dji.gt.sdk.dto.Receiver receiverDTO = new com.dji.gt.sdk.dto.Receiver();
            receiverDTO.setConverId(roomId);
            receiverDTO.setGroup(Boolean.TRUE);
            return receiverDTO;
        }).collect(Collectors.toList());
        // 新gt卡片消息
        Map<String, String> templateParams = JSON.parseObject(message.getContent(), new TypeReference<Map<String, String>>() {
        });
        logger.error("templateParams参数 :{}", JSON.toJSONString(templateParams));
        logger.error("sendCardMsgWithTemplate入参 templateId:{},receiverList:{}", templateId, JSON.toJSONString(receiverList));
        TypeReference<JSONObject> resultClz = new TypeReference<JSONObject>() {
        };
        JSONObject jsonObject = gtClient.sendCardMsgWithTemplate(String.valueOf(templateId), templateParams, receiverList, resultClz);
        logger.error("sendCardMsgWithTemplate结果 :{}", jsonObject);

    }


    /**
     * 通过网关请求发送消息给GT
     *
     * @param name    目标用户的域账号AD
     * @param message 消息信息
     * <AUTHOR>
     */
    private void sendHttpToGt(List<String> name, Message message) throws Exception {
        GTRequestVO gtRequestVO = new GTRequestVO();
        gtRequestVO.setAppId(this.messageConfig.getAppId());
        gtRequestVO.setBizId(message.getMessageId().toString());
        gtRequestVO.setContent(message.getContent());
        gtRequestVO.setToads(name);
        String payload = JSON.toJSONString(gtRequestVO);
        GatewayClient client = new GatewayClient(this.messageConfig.getUrl(), this.messageConfig.getGwId(), this.messageConfig.getGwSecret());
        String invokeUrl = SpfmConstants.InvokeUrl.GT_SEND_POST;
        Map<String, String> headers = new HashMap<String, String>();
        Response respPost = client.httpPost(invokeUrl, "", headers, payload, "json");
        logger.info("GT Message results:{}", respPost);
        if (respPost.getStatus() != SUCCESS_CODE) {
            logger.error("GT send Message status {},error message {}", respPost.getStatus(), respPost.getContent());
        }
    }
}
