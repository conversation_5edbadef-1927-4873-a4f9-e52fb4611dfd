package org.hzero.message.api.dto;

import io.swagger.annotations.*;
import lombok.*;

import javax.persistence.*;
import javax.validation.constraints.*;

/**
 * description
 *
 * <AUTHOR> 2021/05/13 18:32
 */
@Data
public class DjiInterfaceLogDtlDTO {
	@ApiModelProperty("表ID，主键")
	@Id
	@GeneratedValue
	private Long interfaceLogDtlId;
	@ApiModelProperty(
			value = "租户ID",
			required = true
	)
	@NotNull
	private Long tenantId;
	@ApiModelProperty(
			value = "接口日志ID",
			required = true
	)
	@NotNull
	private Long interfaceLogId;
	@ApiModelProperty(
			value = "调用记录唯一标识，UUID",
			required = true
	)
	@NotBlank
	private String invokeKey;
	@ApiModelProperty("接口请求头参数")
	private String interfaceReqHeaderParam;
	@ApiModelProperty("接口请求Body参数")
	private String interfaceReqBodyParam;
	@ApiModelProperty("接口响应内容")
	private String interfaceRespContent;
	@ApiModelProperty("请求头参数")
	private String reqHeaderParam;
	@ApiModelProperty("请求Body参数")
	private String reqBodyParam;
	@ApiModelProperty("响应内容")
	private String respContent;
	@ApiModelProperty("异常堆栈")
	private String stacktrace;
	@ApiModelProperty("备注说明")
	private String remark;
}
