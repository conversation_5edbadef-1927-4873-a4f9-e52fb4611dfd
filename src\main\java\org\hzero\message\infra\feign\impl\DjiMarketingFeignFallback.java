package org.hzero.message.infra.feign.impl;

import feign.hystrix.FallbackFactory;
import org.hzero.boot.message.entity.Attachment;
import org.hzero.message.infra.feign.DjiMarketingFeign;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class DjiMarketingFeignFallback implements FallbackFactory<DjiMarketingFeign> {

    private static final Logger LOGGER = LoggerFactory.getLogger(DjiMarketingFeignFallback.class);

    @Override
    public DjiMarketingFeign create(Throwable throwable) {

        return new DjiMarketingFeign() {
            @Override
            public Attachment transformCmPdf(String cmNumber) {
                LOGGER.error("message service create has error", throwable);
                return null;
            }

            @Override
            public void updateCmStatus(String cmPiId) {
                LOGGER.error("message service updateCmStatus has error", throwable);
            }

            @Override
            public String getCmInfoList(String cmNumber) {
                LOGGER.error("message service getCmInfoList has error", throwable);
                return null;
            }
        };
    }
}
