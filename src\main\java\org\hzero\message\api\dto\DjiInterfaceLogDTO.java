package org.hzero.message.api.dto;

import com.fasterxml.jackson.annotation.*;
import io.swagger.annotations.*;
import lombok.*;
import org.hzero.boot.platform.lov.annotation.*;
import org.springframework.format.annotation.*;

import java.util.*;

/**
 * description
 *
 * <AUTHOR> 2021/05/13 18:31
 */
@Data
public class DjiInterfaceLogDTO {
	@ApiModelProperty("表ID，主键")
	private Long interfaceLogId;
	@ApiModelProperty(
			value = "租户ID",
			required = true
	)
	private Long tenantId;
	@ApiModelProperty("调用记录唯一标识，UUID")
	private String invokeKey;
	@ApiModelProperty("调用类型，代码HITF.INVOKE_TYPE")
	@LovValue(
			value = "HITF.INVOKE_TYPE",
			meaningField = "invokeTypeMeaning"
	)
	private String invokeType;
	@ApiModelProperty("调用来源ID，例如，测试用例类型，此处即为测试用例ID")
	private Long invokeSourceId;
	@ApiModelProperty("应用代码")
	private String applicationCode;
	@ApiModelProperty("应用名称")
	private String applicationName;
	@ApiModelProperty("服务代码")
	private String serverCode;
	@ApiModelProperty("服务名称")
	private String serverName;
	@ApiModelProperty("客户端ID")
	private String clientId;
	@ApiModelProperty("接口代码")
	private String interfaceCode;
	@ApiModelProperty("接口类型")
	private String interfaceType;
	@ApiModelProperty("接口路径，服务器透传后的接口路径")
	private String interfaceUrl;
	@ApiModelProperty("接口请求方式，代码：HITF.REQUEST_METHOD")
	private String interfaceRequestMethod;
	@ApiModelProperty("接口请求时间")
	private Date interfaceRequestTime;
	@ApiModelProperty("接口响应耗时")
	private Long interfaceResponseTime;
	@ApiModelProperty("接口响应状态代码")
	private String interfaceResponseCode;
	@ApiModelProperty("接口相应状态")
	private String interfaceResponseStatus;
	@ApiModelProperty("请求方式，代码：HITF.REQUEST_METHOD")
	private String requestMethod;
	@ApiModelProperty("接口ID")
	private Long interfaceId;
	@ApiModelProperty("接口名称")
	private String interfaceName;
	@ApiModelProperty("服务ID")
	private Long interfaceServerId;
	@ApiModelProperty("请求时间")
	@JsonFormat(
			pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@DateTimeFormat(
			pattern = "yyyy-MM-dd HH:mm:ss"
	)
	private Date requestTime;
	@ApiModelProperty("请求响应耗时")
	private Long responseTime;
	@ApiModelProperty("请求响应状态代码")
	private String responseCode;
	@ApiModelProperty("请求响应状态")
	private String responseStatus;
	@ApiModelProperty("IP地址")
	private String ip;
	@ApiModelProperty("referer")
	private String referer;
	@ApiModelProperty("userAgent")
	private String userAgent;
	private DjiInterfaceLogDtlDTO interfaceLogDtlDTO;
}

