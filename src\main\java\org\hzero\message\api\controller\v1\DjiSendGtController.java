package org.hzero.message.api.controller.v1;

import io.choerodon.core.iam.*;
import io.choerodon.swagger.annotation.*;
import io.swagger.annotations.*;
import org.hzero.boot.message.entity.*;
import org.hzero.message.app.service.*;
import org.hzero.message.config.*;
import org.hzero.message.domain.entity.Message;
import org.springframework.beans.factory.annotation.*;
import org.springframework.web.bind.annotation.*;

/**
 * description
 *
 * <AUTHOR> 2021/04/12 15:36
 */
@Api(tags = DjiMessageSwaggerApiConfig.SEND_GT)
@RestController("djiSendGtController.v1")
@RequestMapping("/v1/dji-send-gt")
public class DjiSendGtController {

	@Autowired
	private GtSendService sendService;

	@ApiOperation(value = "GT消息发送")
	@Permission(level = ResourceLevel.ORGANIZATION)
	@PostMapping
	public Message sendGt(@RequestBody MessageSender messageSender) {
		return sendService.sendMessage(messageSender);
	}

	@ApiOperation(value = "GT消息发送无权限")
	@Permission(permissionPublic = true)
	@PostMapping("/NoAuth")
	public Message sendGtNoAuth(@RequestBody MessageSender messageSender) {
		return sendService.sendMessage(messageSender);
	}

	@ApiOperation(value = "GT消息发群消息")
	@Permission(permissionPublic = true)
	@PostMapping("/sendGtGroup")
	public Message sendGtGroup(@RequestBody MessageSender messageSender) {
		return sendService.sendGtGroupMessage(messageSender);
	}
}
