package org.hzero.boot.imported.infra.listener;

import io.choerodon.core.convertor.*;
import org.hzero.boot.imported.app.service.*;
import org.hzero.boot.imported.infra.registry.*;
import org.hzero.boot.imported.infra.validator.annotation.*;
import org.slf4j.*;
import org.springframework.boot.*;
import org.springframework.context.*;
import org.springframework.data.util.*;
import org.springframework.stereotype.*;
import org.springframework.util.*;

import java.util.*;

/**
 * <p>
 * 扩展校验注册Spring监听
 * </p>
 *
 * <AUTHOR> 2020/12/03 15:09
 */
@Component
public class RegistryApplicationExtListener implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(RegistryApplicationExtListener.class);

    @Override
    public void run(String... args) throws Exception {
        ApplicationContext applicationContext = ApplicationContextHelper.getContext();
        Map<String, Object> beans = applicationContext.getBeansWithAnnotation(ImportValidators.class);
        for (Object bean : beans.values()) {
            if (bean instanceof BatchValidatorHandler) {
                ImportValidators batchValidator = ProxyUtils.getUserClass(bean).getAnnotation(ImportValidators.class);
                if (ObjectUtils.isEmpty(batchValidator)) {
                    logger.debug("could not get target bean , service : {}", batchValidator);
                } else {
                    ImportValidator[] validators = batchValidator.value();
                    for (ImportValidator validator : validators) {
                        ImportExtRegistry.addValidator(validator.templateCode(), validator.tenantNum(),
                                        validator.sheetIndex(), validator.sheetName(), (BatchValidatorHandler) bean);
                    }
                }
            } else if (bean instanceof AbstractFullValidatorHandler) {
                ImportValidators batchValidator = ProxyUtils.getUserClass(bean).getAnnotation(ImportValidators.class);
                if (ObjectUtils.isEmpty(batchValidator)) {
                    logger.debug("could not get target bean , service : {}", batchValidator);
                } else {
                    ImportValidator[] validators = batchValidator.value();
                    for (ImportValidator validator : validators) {
                        ImportExtRegistry.addFullValidator(validator.templateCode(), validator.tenantNum(),
                                        validator.sheetIndex(), validator.sheetName(),
                                        (AbstractFullValidatorHandler) bean);
                    }
                }
            }
        }
    }
}
