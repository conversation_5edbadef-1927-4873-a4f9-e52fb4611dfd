package com.dji.message.config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class XssFilterConfig {
    @Bean
    public FilterRegistrationBean<XssFilter> filterRegistration(){
        // 新建过滤器注册类
        FilterRegistrationBean<XssFilter> registration = new FilterRegistrationBean<>();
        // 添加自定义 过滤器
        registration.setFilter(XssFilter());
        // 设置过滤器的URL模式
        registration.addUrlPatterns("/v1/0/notices","/v1/0/message/templates");
        //设置过滤器顺序
        registration.setOrder(12753);
        return registration;
    }
    @Bean
    public XssFilter XssFilter(){
        return new XssFilter();
    }
}
