package org.hzero.message.api.controller.v1;

import io.choerodon.swagger.annotation.Permission;
import io.swagger.annotations.ApiOperation;
import org.hzero.core.util.Results;
import org.hzero.message.api.controller.v1.dto.SmsSendDTO;
import org.hzero.message.app.service.SmsSendV2Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: c-li.xiao
 * @Description:短信发送接入ums
 * @Date: 2025/10/16 14:18
 */
@RestController("sendSMSController.v1")
@RequestMapping("/v1/send-sms")
public class SendSMSController {

	@Autowired
	private SmsSendV2Service sendService;

	@ApiOperation(value = "手机短信消息发送")
	@Permission(permissionPublic = true)
	@PostMapping
	public ResponseEntity<String> sendPhoneSms(@RequestBody SmsSendDTO sendDTO) {
		return Results.success(sendService.sendPhoneSms(sendDTO));
	}

}
