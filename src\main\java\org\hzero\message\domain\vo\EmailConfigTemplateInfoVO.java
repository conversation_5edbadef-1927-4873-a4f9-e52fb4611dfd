

package org.hzero.message.domain.vo;

import io.choerodon.mybatis.domain.*;
import io.swagger.annotations.*;
import lombok.*;
import lombok.experimental.*;
import org.hzero.core.cache.*;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("邮件规则配置+模版信息返回vo")
public class EmailConfigTemplateInfoVO {

    @ApiModelProperty(value = "id")
    private String ruleConfigId;

    @ApiModelProperty(value = "场景id")
    private String sceneId;

    @ApiModelProperty(value = "组别id")
    private Long groupId;

    @ApiModelProperty(value = "语种")
    private String language;

    @ApiModelProperty(value = "发件邮箱")
    private String sendMailBox;

    @ApiModelProperty(value = "发件邮箱名称")
    private String sendMailBoxName;

    @ApiModelProperty("消息模板ID")
    private Long templateId;

    @ApiModelProperty(value = "消息模板编码")
    private String templateCode;

    @ApiModelProperty(value = "消息模板名称")
    private String templateName;

    @ApiModelProperty(value = "发件落款")
    private String signName;

    @ApiModelProperty(value = "'邮箱发送人")
    private String sender;

    @ApiModelProperty(value = "'模版标题")
    private String templateTitle;

    @ApiModelProperty(value = "'模版内容")
    private String templateContent;

}
