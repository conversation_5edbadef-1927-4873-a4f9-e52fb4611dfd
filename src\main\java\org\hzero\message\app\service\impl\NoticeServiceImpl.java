

package org.hzero.message.app.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.choerodon.core.domain.Page;
import io.choerodon.core.exception.CommonException;
import io.choerodon.core.oauth.CustomUserDetails;
import io.choerodon.core.oauth.DetailsHelper;
import io.choerodon.mybatis.pagehelper.domain.PageRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.hzero.boot.message.entity.Receiver;
import org.hzero.core.base.BaseConstants.Flag;
import org.hzero.core.convert.CommonConverter;
import org.hzero.core.helper.LanguageHelper;
import org.hzero.core.redis.RedisHelper;
import org.hzero.core.util.EscapeUtils;
import org.hzero.message.api.dto.NoticeDTO;
import org.hzero.message.api.dto.SimpleMessageDTO;
import org.hzero.message.app.service.NoticeService;
import org.hzero.message.app.service.UserMessageService;
import org.hzero.message.config.MessageConfigProperties;
import org.hzero.message.domain.entity.Notice;
import org.hzero.message.domain.entity.NoticeContent;
import org.hzero.message.domain.repository.NoticeContentRepository;
import org.hzero.message.domain.repository.NoticeRepository;
import org.hzero.message.domain.repository.UserMessageRepository;
import org.hzero.message.infra.constant.ErrorCodeConstant;
import org.hzero.message.infra.mapper.NoticeMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class NoticeServiceImpl implements NoticeService {
    @Autowired
    private NoticeRepository noticeRepository;
    @Autowired
    private NoticeContentRepository noticeContentRepository;
    @Autowired
    private RedisHelper redisHelper;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private UserMessageService userMessageService;
    @Autowired
    private UserMessageRepository userMessageRepository;
    @Autowired
    private MessageConfigProperties messageConfigProperties;
    @Autowired
    private NoticeMapper noticeMapper;

    private static final String BULLETIN_SET = "hmsg:published_notice_user_set:";

    public NoticeServiceImpl() {
    }

    @Transactional(
        rollbackFor = {Exception.class}
    )
    public NoticeDTO createNotice(NoticeDTO noticeDTO) {
        Notice notice = (Notice)CommonConverter.beanConvert(Notice.class, noticeDTO);
        notice.setStatusCode("DRAFT");
        if (notice.getStickyFlag() == null) {
            notice.setStickyFlag(Flag.NO);
        }
        if (StringUtils.isBlank(noticeDTO.getRemindTypeCode()) ||
                (!StringUtils.equals("None",noticeDTO.getRemindTypeCode()) && StringUtils.isBlank(noticeDTO.getNoticeContent().getSecondBody()))){
            throw new CommonException(ErrorCodeConstant.ERROR_ATTACH_NOT_EXIST);
        }
        this.noticeRepository.insert(notice);
        BeanUtils.copyProperties(notice, noticeDTO);
        NoticeContent noticeContent = noticeDTO.getNoticeContent();
        noticeContent.setTenantId(noticeDTO.getTenantId()).setNoticeId(noticeDTO.getNoticeId());
        noticeContent.setNoticeBody(EscapeUtils.preventScript(noticeContent.getNoticeBody()));
        noticeContent.setSecondBody(EscapeUtils.preventScript(noticeContent.getSecondBody()));
        this.noticeContentRepository.insert(noticeContent);
        return noticeDTO;
    }

    @Transactional(
        rollbackFor = {Exception.class}
    )
    public NoticeDTO updateNotice(NoticeDTO noticeDTO) {
        Notice notice = (Notice)CommonConverter.beanConvert(Notice.class, noticeDTO);
        notice.setStatusCode("DRAFT");
        this.noticeRepository.updateByPrimaryKey(notice);
        BeanUtils.copyProperties(notice, noticeDTO);
        NoticeContent noticeContent = noticeDTO.getNoticeContent();
        noticeContent.setNoticeBody(EscapeUtils.preventScript(noticeContent.getNoticeBody()));
        noticeContent.setSecondBody(EscapeUtils.preventScript(noticeContent.getSecondBody()));
        this.noticeContentRepository.updateOptional(noticeContent, new String[]{"noticeBody","secondBody", "receiveTenantId", "recvUserGroupId", "receiveUserId", "recvTenantName",
                "recvGroupName", "receiveUserName"});
        notice.deleteCachePublishedNotice(this.redisHelper);
        return noticeDTO;
    }

    @Transactional(
        rollbackFor = {Exception.class}
    )
    public Notice deleteNotice(Long organizationId, Long noticeId) {
        Notice notice = Notice.updateStatus(this.noticeRepository, noticeId, "DELETED");
        notice.deleteCachePublishedNotice(this.redisHelper);
        return notice;
    }

    @Transactional(
        rollbackFor = {Exception.class}
    )
    public Notice publicNotice(Long organizationId, Long noticeId) {
        Notice notice = Notice.updateStatus(this.noticeRepository, noticeId, "PUBLISHED");
        notice.refreshCachePublishedNotices(this.redisHelper, this.objectMapper);
        List<Receiver> userList = this.userMessageRepository.getAllUser(organizationId);
        NoticeDTO dto = this.noticeRepository.detailAnnouncement(organizationId, noticeId);
        Iterator var6 = userList.iterator();

        while(var6.hasNext()) {
            Receiver receiver = (Receiver)var6.next();
            this.userMessageService.createSimpleMessage(receiver.getTargetUserTenantId(), receiver.getUserId(), (new SimpleMessageDTO()).setUserMessageId(noticeId).setNoticeId(noticeId).setUserMessageTypeCode(dto.getReceiverTypeCode()).setUserMessageTypeMeaning(dto.getReceiverTypeMeaning()).setSubject(dto.getTitle()).setTenantId(receiver.getTargetUserTenantId()).setCreationDate(notice.getPublishedDate()));
        }

        return notice;
    }

    @Transactional(
        rollbackFor = {Exception.class}
    )
    public Notice revokeNotice(Long organizationId, Long noticeId) {
        Notice notice = Notice.updateStatus(this.noticeRepository, noticeId, "DRAFT");
        notice.refreshCachePublishedNotices(this.redisHelper, this.objectMapper);
        return notice;
    }

    public Page<NoticeDTO> pageNoticeTitle(String category, String title, Long organizationId, PageRequest pageRequest) {
        String lang = LanguageHelper.language();
        pageRequest.setSize(10);
        return this.noticeRepository.pageNoticeTitle(category, lang, title, organizationId, pageRequest);
    }

    public List<NoticeDTO> listUserAnnouncement(NoticeDTO noticeDTO) {
        List<NoticeDTO> simpleNoticeList = this.noticeRepository.listUserAnnouncement(noticeDTO);
        return (List)simpleNoticeList.stream().filter((item) -> {
            return item.getPublishedDate() != null;
        }).sorted(Comparator.comparing(NoticeDTO::getPublishedDate).reversed()).limit(noticeDTO.getPreviewCount() != null ? (long)noticeDTO.getPreviewCount() : this.messageConfigProperties.getMaxUnreadMessageCount()).collect(Collectors.toList());
    }

    public NoticeDTO topAnnouncement(NoticeDTO noticeDTO) {
        noticeDTO.setStickyFlag(Flag.YES);
        List<NoticeDTO> simpleNoticeList = (List)this.noticeRepository.listUserAnnouncement(noticeDTO).stream().filter((item) -> {
            return item.getPublishedDate() != null;
        }).sorted(Comparator.comparing(NoticeDTO::getPublishedDate).reversed()).limit(1L).collect(Collectors.toList());
        return CollectionUtils.isEmpty(simpleNoticeList) ? null : (NoticeDTO)simpleNoticeList.get(0);
    }

    public Page<NoticeDTO> pageNotice(PageRequest pageRequest, NoticeDTO noticeDTO) {
        if (Boolean.parseBoolean(noticeDTO.getUserNotice())) {
            noticeDTO.setUserId(DetailsHelper.getUserDetails().getUserId());
            return this.noticeRepository.pageUserAnnouncement(pageRequest, noticeDTO);
        } else {
            return this.noticeRepository.pageNotice(pageRequest, noticeDTO);
        }
    }

    @Override
    public NoticeDTO bulletin() {
        // 1.查询最新一次发布且需要弹的公告
        CustomUserDetails userDetails = DetailsHelper.getUserDetails();
        NoticeDTO noticeDTO = new NoticeDTO();
        noticeDTO.setUserId(userDetails.getUserId());
        noticeDTO.setTenantId(0L);
        noticeDTO.setNow(new Date());
        NoticeDTO noticeByUser = noticeMapper.getNoticeByUser(noticeDTO);
        if (Objects.isNull(noticeByUser) || Objects.isNull(noticeByUser.getNoticeId())){
            log.info("当前操作人未查询到需要发布的公告,当前操作人id:{}",userDetails.getUserId());
            return null;
        }
        log.info("查询到最新一条公告,当前操作人id:{},公告id:{}",userDetails.getUserId(),noticeByUser.getNoticeId());
        // 2.判断当前人是否需要弹
        Set<String> userIds = redisHelper.setMembers(BULLETIN_SET + noticeByUser.getNoticeId());
        if (CollectionUtils.isEmpty(userIds) || !userIds.contains(String.valueOf(userDetails.getUserId()))){
            log.info("当前操作人无需弹公告或公告已弹,当前操作人id:{}",userDetails.getUserId());
            return null;
        }
        // 3.删除set中的key
        redisHelper.setDel(BULLETIN_SET + noticeByUser.getNoticeId(),String.valueOf(userDetails.getUserId()));
        // 4.返回结果
        return noticeByUser;
    }
}
