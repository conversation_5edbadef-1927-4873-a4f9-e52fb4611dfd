package org.hzero.boot.imported.api.controller.v1;

import com.fasterxml.jackson.core.type.*;
import com.fasterxml.jackson.databind.*;
import io.choerodon.core.domain.*;
import io.choerodon.core.exception.*;
import io.choerodon.core.iam.*;
import io.choerodon.mybatis.pagehelper.annotation.*;
import io.choerodon.mybatis.pagehelper.domain.*;
import io.choerodon.swagger.annotation.*;
import io.swagger.annotations.*;
import org.apache.commons.lang3.*;
import org.hzero.boot.imported.api.dto.*;
import org.hzero.boot.imported.app.service.*;
import org.hzero.boot.imported.config.*;
import org.hzero.boot.imported.domain.entity.*;
import org.hzero.boot.imported.domain.repository.*;
import org.hzero.boot.imported.infra.constant.*;
import org.hzero.boot.imported.infra.enums.*;
import org.hzero.boot.platform.lov.annotation.*;
import org.hzero.core.base.*;
import org.hzero.core.util.*;
import org.hzero.starter.keyencrypt.core.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.*;
import springfox.documentation.annotations.*;

import java.io.*;
import java.util.*;


/**
 * 通用导入Controller
 *
 * <AUTHOR>
 */
@RestController("importController.v1")
@RequestMapping(value = "/v1/{organizationId}/import/data")
@Api(tags = ImportClientApiConfig.IMPORT_DATA)
@ApiIgnore
public class DataImportController {

    @Autowired
    private ImportDataService importDataService;
    @Autowired
    private ImportDataExtService importDataExtService;
    @Autowired
    private ImportRepository importRepository;
    @Autowired
    private ImportDataRepository importDataRepository;
    @Autowired
    private ObjectMapper objectMapper;

    @ApiOperation(value = "从文件导入临时表(异步)")
    @PostMapping("/data-upload")
    @Permission(level = ResourceLevel.ORGANIZATION, permissionLogin = true)
    public ResponseEntity<String> uploadData(@PathVariable @ApiParam(value = "租户Id", required = true) Long organizationId,
                                             @RequestParam @ApiParam(value = "模板编码", required = true) String templateCode,
                                             @RequestParam(required = false) @ApiParam(value = "自定义参数") String param,
                                             @ApiParam(value = "excel") MultipartFile excel) throws IOException {
        // 校验自定义参数
        if (StringUtils.isNotBlank(param)) {
            try {
                objectMapper.readValue(param, new TypeReference<Map<String, Object>>() {
                });
            } catch (IOException e) {
                throw new CommonException(HimpBootConstants.ErrorCode.PARAM, e);
            }
        }
        return Results.success(importDataService.uploadData(organizationId, templateCode, param, excel));
    }

    @ApiOperation(value = "验证临时表数据(异步)")
    @PostMapping("/data-validate")
    @Permission(level = ResourceLevel.ORGANIZATION, permissionLogin = true)
    @ProcessLovValue(targetField = BaseConstants.FIELD_BODY)
    public ResponseEntity<Import> validateData(@PathVariable @ApiParam(value = "租户Id", required = true) Long organizationId,
                                               @RequestParam @ApiParam(value = "模板编码", required = true) String templateCode,
                                               @RequestParam @ApiParam(value = "批次号", required = true) String batch,
                                               @RequestBody(required = false) Map<String, Object> args) {
        return Results.success(importDataExtService.validateData(organizationId, templateCode, batch, ObjectUtils.defaultIfNull(args, new HashMap<>(4))));
    }

    @ApiOperation(value = "从临时表导入正式表(异步)")
    @PostMapping("/data-import")
    @Permission(level = ResourceLevel.ORGANIZATION, permissionLogin = true)
    @ProcessLovValue(targetField = BaseConstants.FIELD_BODY)
    public ResponseEntity<ImportDTO> importData(@PathVariable @ApiParam(value = "租户Id", required = true) Long organizationId,
                                                @RequestParam @ApiParam(value = "模板编码", required = true) String templateCode,
                                                @RequestParam @ApiParam(value = "批次号", required = true) String batch,
                                                @RequestBody(required = false) Map<String, Object> args) {
        return Results.success(importDataService.importData(organizationId, templateCode, batch, ObjectUtils.defaultIfNull(args, new HashMap<>(4))));
    }

    @ApiOperation(value = "查询数据")
    @GetMapping
    @Permission(level = ResourceLevel.ORGANIZATION, permissionLogin = true)
    @CustomPageRequest
    @ProcessLovValue(targetField = BaseConstants.FIELD_BODY)
    public ResponseEntity<Page<ImportData>> pageData(@RequestParam @ApiParam(value = "模板编码", required = true) String templateCode,
                                                     @RequestParam @ApiParam(value = "批次号", required = true) String batch,
                                                     @RequestParam(required = false) Integer sheetIndex,
                                                     @RequestParam(required = false) DataStatus status,
                                                     @ApiIgnore @SortDefault(value = ImportData.FIELD_ID) PageRequest pageRequest) {
        return Results.success(importDataService.pageData(templateCode, batch, sheetIndex, status, pageRequest));
    }

    @ApiOperation(value = "更新单条数据")
    @PutMapping("/{id}")
    @Permission(level = ResourceLevel.ORGANIZATION, permissionLogin = true)
    public ResponseEntity<String> updateImportData(@PathVariable @ApiParam(value = "租户Id", required = true) Long organizationId,
                                                   @PathVariable(value = "id") @ApiParam(value = "临时数据Id", required = true) @Encrypt Long id,
                                                   @RequestBody String data) {
        return Results.success(importDataService.updateImportData(id, data));
    }

    @ApiOperation(value = "删除单条数据")
    @DeleteMapping("/{id}")
    @Permission(level = ResourceLevel.ORGANIZATION, permissionLogin = true)
    public ResponseEntity<String> deleteImportData(@PathVariable @ApiParam(value = "租户Id", required = true) Long organizationId,
                                                   @PathVariable(value = "id") @ApiParam(value = "临时数据Id", required = true) @Encrypt Long id) {
        importDataService.deleteById(id);
        return Results.success();
    }

    @ApiOperation(value = "状态查询")
    @Permission(level = ResourceLevel.ORGANIZATION, permissionLogin = true)
    @GetMapping("/status")
    @ProcessLovValue(targetField = BaseConstants.FIELD_BODY)
    public ResponseEntity<Import> getStatus(@PathVariable @ApiParam(value = "租户Id", required = true) Long organizationId,
                                            @RequestParam @ApiParam(value = "批次", required = true) String batch) {
        return Results.success(importRepository.getStatus(batch));
    }

    @ApiOperation(value = "自动导入到正式表，一次执行三个流程(同步)")
    @PostMapping("/sync/auto-import")
    @Permission(level = ResourceLevel.ORGANIZATION, permissionLogin = true)
    @ProcessLovValue(targetField = BaseConstants.FIELD_BODY)
    public ResponseEntity<String> autoImport(@PathVariable @ApiParam(value = "租户Id", required = true) Long organizationId,
                                             @RequestParam @ApiParam(value = "模板编码", required = true) String templateCode,
                                             @RequestParam(required = false) @ApiParam(value = "自定义参数") String param,
                                             @ApiParam(value = "excel") MultipartFile excel) {
        // 校验自定义参数
        if (StringUtils.isNotBlank(param)) {
            try {
                objectMapper.readValue(param, new TypeReference<Map<String, Object>>() {
                });
            } catch (IOException e) {
                throw new CommonException(HimpBootConstants.ErrorCode.PARAM, e);
            }
        }
        String batch = importDataService.syncUploadData(organizationId, templateCode, param, excel);
        importDataExtService.syncValidateData(organizationId, templateCode, batch, new HashMap<>(4));
        importDataService.syncImportData(organizationId, templateCode, batch, new HashMap<>(4));
        return Results.success(batch);
    }

    @ApiOperation(value = "自动导入到正式表，一次执行三个流程(同步)-要货计划客制化导入")
    @PostMapping("/sync/auto-import-requirement-plan")
    @Permission(level = ResourceLevel.ORGANIZATION, permissionLogin = true)
    @ProcessLovValue(targetField = BaseConstants.FIELD_BODY)
    public ResponseEntity<String> autoImportRequirementPlan(
                    @PathVariable @ApiParam(value = "租户Id", required = true) Long organizationId,
                    @RequestParam @ApiParam(value = "模板编码", required = true) String templateCode,
                    @RequestParam(required = false) @ApiParam(value = "自定义参数") String param,
                    @RequestParam("file") @ApiParam(value = "file") MultipartFile excel) {
        // 校验自定义参数
        if (StringUtils.isNotBlank(param)) {
            try {
                objectMapper.readValue(param, new TypeReference<Map<String, Object>>() {});
            } catch (IOException e) {
                throw new CommonException(HimpBootConstants.ErrorCode.PARAM, e);
            }
        }
        String batch = importDataService.syncUploadData(organizationId, templateCode, param, excel);
        importDataExtService.syncValidateData(organizationId, templateCode, batch, new HashMap<>(4));
        // 判断校验是否有错误信息
        ImportData importDataParam = new ImportData();
        importDataParam.setBatch(batch);
        List<ImportData> validImportDataList = importDataRepository.select(importDataParam);
        Optional<ImportData> validDataOptional = validImportDataList.stream()
                        .filter(validImportData -> Objects.nonNull(validImportData.getErrorMsg())).findFirst();
        if (validDataOptional.isPresent()) {
            throw new CommonException(validDataOptional.get().getErrorMsg());
        }
        importDataService.syncImportData(organizationId, templateCode, batch, new HashMap<>(4));
        // 判断导入是否有错误信息
        importDataParam.setTemplateCode(templateCode);
        List<ImportData> importDataList = importDataRepository.select(importDataParam);
        Optional<ImportData> importDataOptional = importDataList.stream()
                        .filter(importData -> Objects.nonNull(importData.getErrorMsg())).findFirst();
        if (importDataOptional.isPresent()) {
            throw new CommonException(importDataOptional.get().getErrorMsg());
        }
        return Results.success(batch);
    }

    @ApiOperation(value = "从文件导入临时表(同步)")
    @PostMapping("/sync/data-upload")
    @Permission(level = ResourceLevel.ORGANIZATION, permissionLogin = true)
    public ResponseEntity<String> syncUploadData(@PathVariable @ApiParam(value = "租户Id", required = true) Long organizationId,
                                                 @RequestParam @ApiParam(value = "模板编码", required = true) String templateCode,
                                                 @RequestParam(required = false) @ApiParam(value = "自定义参数") String param,
                                                 @ApiParam(value = "excel") MultipartFile excel) {
        // 校验自定义参数
        if (StringUtils.isNotBlank(param)) {
            try {
                objectMapper.readValue(param, new TypeReference<Map<String, Object>>() {
                });
            } catch (IOException e) {
                throw new CommonException(HimpBootConstants.ErrorCode.PARAM, e);
            }
        }
        return Results.success(importDataService.syncUploadData(organizationId, templateCode, param, excel));
    }

    @ApiOperation(value = "验证临时表数据(同步)")
    @PostMapping("/sync/data-validate")
    @Permission(level = ResourceLevel.ORGANIZATION, permissionLogin = true)
    @ProcessLovValue(targetField = BaseConstants.FIELD_BODY)
    public ResponseEntity<Import> syncValidateData(@PathVariable @ApiParam(value = "租户Id", required = true) Long organizationId,
                                                   @RequestParam @ApiParam(value = "模板编码", required = true) String templateCode,
                                                   @RequestParam @ApiParam(value = "批次号", required = true) String batch,
                                                   @RequestBody(required = false) Map<String, Object> args) {
        return Results.success(importDataService.syncValidateData(organizationId, templateCode, batch, ObjectUtils.defaultIfNull(args, new HashMap<>(4))));
    }

    @ApiOperation(value = "从临时表导入正式表(同步)")
    @PostMapping("/sync/data-import")
    @Permission(level = ResourceLevel.ORGANIZATION, permissionLogin = true)
    @ProcessLovValue(targetField = BaseConstants.FIELD_BODY)
    public ResponseEntity<ImportDTO> syncImportData(@PathVariable @ApiParam(value = "租户Id", required = true) Long organizationId,
                                                    @RequestParam @ApiParam(value = "模板编码", required = true) String templateCode,
                                                    @RequestParam @ApiParam(value = "批次号", required = true) String batch,
                                                    @RequestBody(required = false) Map<String, Object> args) {
        return Results.success(importDataService.syncImportData(organizationId, templateCode, batch, ObjectUtils.defaultIfNull(args, new HashMap<>(4))));
    }
}