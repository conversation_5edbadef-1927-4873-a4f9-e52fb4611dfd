import com.alibaba.fastjson.*;
import com.ctrip.framework.apollo.*;
import com.ctrip.framework.apollo.Config;
import com.dji.dava.dto.Result;
import com.dji.dava.gateway.cloudbus.*;
import io.choerodon.core.domain.*;
import io.choerodon.core.oauth.*;
import io.choerodon.mybatis.domain.*;
import io.choerodon.mybatis.helper.*;
import io.choerodon.mybatis.pagehelper.domain.*;
import org.hzero.boot.imported.domain.entity.*;
import org.hzero.boot.platform.lov.adapter.*;
import org.hzero.boot.platform.lov.dto.*;
import org.hzero.message.api.dto.*;
import org.hzero.message.app.service.*;
import org.hzero.message.app.service.impl.*;
import org.hzero.message.domain.entity.*;
import org.hzero.message.domain.repository.*;
import org.hzero.message.domain.vo.*;
import org.hzero.message.infra.feign.*;
import org.hzero.mybatis.domian.*;
import org.hzero.mybatis.util.*;
import org.junit.*;
import org.junit.runner.*;
import org.mockito.*;
import org.mockito.invocation.*;
import org.powermock.api.mockito.*;
import org.powermock.core.classloader.annotations.*;
import org.powermock.modules.junit4.*;
import org.springframework.http.*;

import java.util.*;
import java.util.concurrent.*;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"com.sun.org.apache.xerces.*", "javax.xml.*", "org.xml.*", "javax.management.*", "org.w3c.*"})
@PrepareForTest({DetailsHelper.class, CustomUserDetails.class, EntityHelper.class, Condition.Builder.class,
        Condition.class, Sqls.class, ConfigService.class})
public class EmailRuleConfigServiceImplTest {

    @InjectMocks
    private EmailRuleConfigServiceImpl emailRuleConfigService;

    @Mock
    private EmailRuleConfigRepository emailRuleConfigRepository;
    @Mock
    private MessageTemplateRepository messageTemplateRepository;
    @Mock
    private EmailServerRepository emailServerRepository;
    @Mock
    private LovAdapter lovAdapter;
    @Mock
    private RemoteInvoke rmiClient;
    @Mock
    private DjiInterfaceFeign djiInterfaceFeign;
    @Mock
    private EmailSendService emailSendService;

    private Config config;


    @Before
    public void setUp() {
        // 数据操作mock
        PowerMockito.mockStatic(DetailsHelper.class);
        PowerMockito.mockStatic(CustomUserDetails.class);
        CustomUserDetails customUserDetails = new CustomUserDetails("test1", "test1");
        customUserDetails.setRealName("ag-test1");
        PowerMockito.when(DetailsHelper.getUserDetails()).thenReturn(customUserDetails);

        PowerMockito.mockStatic(EntityHelper.class);
        PowerMockito.mockStatic(Condition.class);
        PowerMockito.mockStatic(Sqls.class);
        PowerMockito.when(EntityHelper.getTableByEntity(AuditDomain.class))
                .thenReturn(new EntityTable(AuditDomain.class));
        Condition condition = PowerMockito.mock(Condition.class);
        Condition.Builder cb = PowerMockito.mock(Condition.Builder.class);
        PowerMockito.when(Condition.builder(any())).thenReturn(cb);
        PowerMockito.when(cb.build()).thenReturn(condition);
        PowerMockito.when(cb.select(any())).thenReturn(cb);
        Sqls sqls = PowerMockito.mock(Sqls.class);
        PowerMockito.when(cb.andWhere(sqls)).thenReturn(cb);
        PowerMockito.when(Sqls.custom()).thenReturn(sqls);
        PowerMockito.when(sqls.andIn(any(), any())).thenReturn(sqls);
        PowerMockito.when(sqls.andEqualTo(any(), any())).thenReturn(sqls);
        PowerMockito.when(sqls.andNotEqualTo(any(), any())).thenReturn(sqls);
        PowerMockito.when(sqls.andNotEqualTo(any(), any(), anyBoolean())).thenReturn(sqls);
        PowerMockito.when(sqls.andLessThanOrEqualTo(any(), any())).thenReturn(sqls);
        PowerMockito.when(sqls.andGreaterThanOrEqualTo(any(), any())).thenReturn(sqls);
        PowerMockito.when(sqls.andLessThan(any(), any())).thenReturn(sqls);

        config = new Config() {
            @Override
            public String getProperty(String key, String defaultValue) {
                return "test1";
            }

            @Override
            public Integer getIntProperty(String key, Integer defaultValue) {
                return null;
            }

            @Override
            public Long getLongProperty(String key, Long defaultValue) {
                return null;
            }

            @Override
            public Short getShortProperty(String key, Short defaultValue) {
                return null;
            }

            @Override
            public Float getFloatProperty(String key, Float defaultValue) {
                return null;
            }

            @Override
            public Double getDoubleProperty(String key, Double defaultValue) {
                return null;
            }

            @Override
            public Byte getByteProperty(String key, Byte defaultValue) {
                return null;
            }

            @Override
            public Boolean getBooleanProperty(String key, Boolean defaultValue) {
                return true;
            }

            @Override
            public String[] getArrayProperty(String key, String delimiter, String[] defaultValue) {
                return new String[0];
            }

            @Override
            public Date getDateProperty(String key, Date defaultValue) {
                return null;
            }

            @Override
            public Date getDateProperty(String key, String format, Date defaultValue) {
                return null;
            }

            @Override
            public Date getDateProperty(String key, String format, Locale locale, Date defaultValue) {
                return null;
            }

            @Override
            public <T extends Enum<T>> T getEnumProperty(String key, Class<T> enumType, T defaultValue) {
                return null;
            }

            @Override
            public long getDurationProperty(String key, long defaultValue) {
                return 0;
            }

            @Override
            public void addChangeListener(ConfigChangeListener listener) {

            }

            @Override
            public Set<String> getPropertyNames() {
                return null;
            }
        };

        PowerMockito.mockStatic(ConfigService.class);
        PowerMockito.when(ConfigService.getAppConfig()).thenReturn(config);

    }

    @Test
    public void queryEmailRuleConfigListPageTest() {
        Page<EmailRuleConfigResVO> emailRuleConfigResPage = new Page<>();
        PowerMockito.when(emailRuleConfigRepository.queryEmailRuleConfigListPage(any(), any())).thenReturn(emailRuleConfigResPage);
        Page<EmailRuleConfigResVO> result = emailRuleConfigService.queryEmailRuleConfigListPage(new PageRequest(), EmailRuleConfigQueryDTO.builder().build());
        assertNotNull(result);
    }

    @Test
    public void findEmailRuleConfigDetailTest() {
        EmailRuleConfig emailRuleConfig = EmailRuleConfig.builder().deleted(false).ruleConfigId("123").build();
        PowerMockito.when(emailRuleConfigRepository.selectByPrimaryKey(any())).thenReturn(emailRuleConfig);
        EmailRuleConfigResVO result = emailRuleConfigService.findEmailRuleConfigDetail("123");
        assertNotNull(result);
    }

    @Test
    public void findEmailRuleConfigDetailTest_error() {
        EmailRuleConfig emailRuleConfig = EmailRuleConfig.builder().deleted(true).ruleConfigId("123").build();
        PowerMockito.when(emailRuleConfigRepository.selectByPrimaryKey(any())).thenReturn(emailRuleConfig);
        try {
            EmailRuleConfigResVO result = emailRuleConfigService.findEmailRuleConfigDetail("123");
            assertNotNull(result);
        } catch (Exception e) {
            Assert.assertEquals("dji.msg.error.data.valid", e.getMessage());
        }

    }

    @Test
    public void createTest_matches_isNull() {
        OrderCustomerGroupVO groupVO = new OrderCustomerGroupVO();
        groupVO.setId(4);
        groupVO.setLocked((byte) 0);
        groupVO.setBrand("DJI");
        Result<String> success = Result.success(JSON.toJSONString(Collections.singletonList(groupVO)));
        PowerMockito.when(rmiClient.getParams(any(), any(), anyMap(), anyMap(), any())).thenReturn(success);
        LovValueDTO lovValueDTO = new LovValueDTO();
        lovValueDTO.setValue("DJI");
        lovValueDTO.setDescription("DJI");
        PowerMockito.when(lovAdapter.queryLovValue(any(), any())).thenReturn(Collections.singletonList(lovValueDTO));
        PowerMockito.when(messageTemplateRepository.selectOne(any())).thenReturn(null);

        try {
            EmailRuleConfigSaveDTO saveDTO = EmailRuleConfigSaveDTO.builder().sceneId("sceneId-").groupId(4L).language("zh_CN").sendMailBox("sendMailBox").build();
            String result = emailRuleConfigService.create(saveDTO);
            assertNotNull(result);
        } catch (Exception e) {
            Assert.assertEquals("dji.msg.error.wrong.data.format", e.getMessage());
        }
    }

    @Test
    public void createTest_messageTemplate_isNull() {
        OrderCustomerGroupVO groupVO = new OrderCustomerGroupVO();
        groupVO.setId(4);
        groupVO.setLocked((byte) 0);
        groupVO.setBrand("DJI");
        Result<String> success = Result.success(JSON.toJSONString(Collections.singletonList(groupVO)));
        PowerMockito.when(rmiClient.getParams(any(), any(), anyMap(), anyMap(), any())).thenReturn(success);
        LovValueDTO lovValueDTO = new LovValueDTO();
        lovValueDTO.setValue("DJI");
        lovValueDTO.setDescription("DJI");
        PowerMockito.when(lovAdapter.queryLovValue(any(), any())).thenReturn(Collections.singletonList(lovValueDTO));
        PowerMockito.when(messageTemplateRepository.selectOne(any())).thenReturn(null);

        try {
            EmailRuleConfigSaveDTO saveDTO = EmailRuleConfigSaveDTO.builder().sceneId("sceneId").groupId(4L).language("zh_CN").sendMailBox("sendMailBox").build();
            String result = emailRuleConfigService.create(saveDTO);
            assertNotNull(result);
        } catch (Exception e) {
            Assert.assertEquals("dji.msg.error.invalid.message.template", e.getMessage());
        }
    }

    @Test
    public void createTest_email_isNull() {
        OrderCustomerGroupVO groupVO = new OrderCustomerGroupVO();
        groupVO.setId(4);
        groupVO.setLocked((byte) 0);
        groupVO.setBrand("DJI");
        Result<String> success = Result.success(JSON.toJSONString(Collections.singletonList(groupVO)));
        PowerMockito.when(rmiClient.getParams(any(), any(), anyMap(), anyMap(), any())).thenReturn(success);
        LovValueDTO lovValueDTO = new LovValueDTO();
        lovValueDTO.setValue("DJI");
        lovValueDTO.setDescription("DJI");
        PowerMockito.when(lovAdapter.queryLovValue(any(), any())).thenReturn(Collections.singletonList(lovValueDTO));
        MessageTemplate selectCondition = (new MessageTemplate()).setTenantId(0L).setTemplateCode("templateCode").setTemplateTitle("templateTitle").setEnabledFlag(1);
        PowerMockito.when(messageTemplateRepository.selectOne(any())).thenReturn(selectCondition);
        PowerMockito.when(emailServerRepository.selectOne(any())).thenReturn(null);

        try {
            EmailRuleConfigSaveDTO saveDTO = EmailRuleConfigSaveDTO.builder().sceneId("sceneId").groupId(4L).language("zh_CN").sendMailBox("sendMailBox").build();
            String result = emailRuleConfigService.create(saveDTO);
            assertNotNull(result);
        } catch (Exception e) {
            Assert.assertEquals("dji.msg.error.invalid.send.email", e.getMessage());
        }

    }

    @Test
    public void createTest() {
        OrderCustomerGroupVO groupVO = new OrderCustomerGroupVO();
        groupVO.setId(4);
        groupVO.setLocked((byte) 0);
        groupVO.setBrand("DJI");
        Result<String> success = Result.success(JSON.toJSONString(Collections.singletonList(groupVO)));
        PowerMockito.when(rmiClient.getParams(any(), any(), anyMap(), anyMap(), any())).thenReturn(success);
        LovValueDTO lovValueDTO = new LovValueDTO();
        lovValueDTO.setValue("DJI");
        lovValueDTO.setDescription("DJI");
        PowerMockito.when(lovAdapter.queryLovValue(any(), any())).thenReturn(Collections.singletonList(lovValueDTO));
        MessageTemplate selectCondition = (new MessageTemplate()).setTenantId(0L).setTemplateCode("templateCode").setTemplateTitle("templateTitle").setEnabledFlag(1);
        PowerMockito.when(messageTemplateRepository.selectOne(any())).thenReturn(selectCondition);
        EmailServer emailServer = (new EmailServer()).setServerCode("sendMailBox").setTenantId(0L).setEnabledFlag(1);
        PowerMockito.when(emailServerRepository.selectOne(any())).thenReturn(emailServer);
        PowerMockito.when(emailRuleConfigRepository.selectByCondition(any())).thenReturn(new ArrayList<>());

        EmailRuleConfigSaveDTO saveDTO = EmailRuleConfigSaveDTO.builder().sceneId("sceneId").groupId(4L).language("zh_CN").sendMailBox("sendMailBox").build();
        String result = emailRuleConfigService.create(saveDTO);
        assertNotNull(result);
    }

    @Test
    public void update_unique_Test() {
        OrderCustomerGroupVO groupVO = new OrderCustomerGroupVO();
        groupVO.setId(4);
        groupVO.setBrand("DJI");
        Result<String> success = Result.success(JSON.toJSONString(Collections.singletonList(groupVO)));
        PowerMockito.when(rmiClient.getParams(any(), any(), anyMap(), anyMap(), any())).thenReturn(success);
        LovValueDTO lovValueDTO = new LovValueDTO();
        lovValueDTO.setValue("DJI");
        lovValueDTO.setDescription("DJI");
        PowerMockito.when(lovAdapter.queryLovValue(any(), any())).thenReturn(Collections.singletonList(lovValueDTO));
        MessageTemplate selectCondition = (new MessageTemplate()).setTenantId(0L).setTemplateCode("templateCode").setTemplateTitle("templateTitle").setEnabledFlag(1);
        PowerMockito.when(messageTemplateRepository.selectOne(any())).thenReturn(selectCondition);
        EmailServer emailServer = (new EmailServer()).setServerCode("sendMailBox").setTenantId(0L).setEnabledFlag(1);
        PowerMockito.when(emailServerRepository.selectOne(any())).thenReturn(emailServer);
        EmailRuleConfig emailRuleConfig = EmailRuleConfig.builder().deleted(false).ruleConfigId("123").build();
        PowerMockito.when(emailRuleConfigRepository.selectByCondition(any())).thenReturn(Collections.singletonList(emailRuleConfig));
        PowerMockito.when(emailRuleConfigRepository.selectByPrimaryKey(any())).thenReturn(emailRuleConfig);

        try {
            EmailRuleConfigUpdateDTO updateDTO = EmailRuleConfigUpdateDTO.builder().ruleConfigId("123").sceneId("sceneId")
                    .groupId(4L).language("zh_CN").sendMailBox("sendMailBox").build();
            String result = emailRuleConfigService.update(updateDTO);
            assertNotNull(result);
        } catch (Exception e) {
            Assert.assertEquals("dji.msg.error.unique.rule.repeats", e.getMessage());
        }
    }

    @Test
    public void update_checkBrandIsolationRisk_Test() {
        OrderCustomerGroupVO groupVO = new OrderCustomerGroupVO();
        groupVO.setId(4);
        groupVO.setLocked((byte) 0);
        groupVO.setBrand("AMFLOW");
        Result<String> success = Result.success(JSON.toJSONString(Collections.singletonList(groupVO)));
        PowerMockito.when(rmiClient.getParams(any(), any(), anyMap(), anyMap(), any())).thenReturn(success);
        List<LovValueDTO> lovValueList = new ArrayList<>();
        LovValueDTO lovValueDTO = new LovValueDTO();
        lovValueDTO.setValue("DJI");
        lovValueDTO.setDescription("DJI");
        LovValueDTO lovValueDTO2 = new LovValueDTO();
        lovValueDTO2.setValue("AMFLOW");
        lovValueDTO2.setDescription("AMFLOW");
        lovValueList.add(lovValueDTO);
        lovValueList.add(lovValueDTO2);
        PowerMockito.when(lovAdapter.queryLovValue(any(), any())).thenReturn(lovValueList);
        PowerMockito.when(rmiClient.getParams(any(), any(), anyMap(), anyMap(), any())).thenReturn(success);
        MessageTemplate selectCondition = (new MessageTemplate()).setTenantId(0L).setTemplateCode("templateCode").setTemplateTitle("templateTitle").setEnabledFlag(1);
        PowerMockito.when(messageTemplateRepository.selectOne(any())).thenReturn(selectCondition);
        EmailServer emailServer = (new EmailServer()).setServerCode("sendMailBox").setTenantId(0L).setEnabledFlag(1);
        PowerMockito.when(emailServerRepository.selectOne(any())).thenReturn(emailServer);
        PowerMockito.when(emailRuleConfigRepository.selectByCondition(any())).thenReturn(new ArrayList<>());
        EmailRuleConfig emailRuleConfig = EmailRuleConfig.builder().deleted(false).ruleConfigId("123").build();
        PowerMockito.when(emailRuleConfigRepository.selectByPrimaryKey(any())).thenReturn(emailRuleConfig);

        try {
            EmailRuleConfigUpdateDTO updateDTO = EmailRuleConfigUpdateDTO.builder().ruleConfigId("123").sceneId("sceneId")
                    .groupId(4L).language("zh_CN").sendMailBox("sendMailBox").signName("signNameDJI").build();
            String result = emailRuleConfigService.update(updateDTO);
            assertNotNull(result);
        } catch (Exception e) {
            Assert.assertEquals("dji.msg.error.the.content.does.not.match.the.brand", e.getMessage());
        }
    }

    @Test
    public void update_checkBrandIsolationRisk2_Test() {
        OrderCustomerGroupVO groupVO = new OrderCustomerGroupVO();
        groupVO.setId(4);
        groupVO.setLocked((byte) 0);
        groupVO.setBrand("AMFLOW");
        Result<String> success = Result.success(JSON.toJSONString(Collections.singletonList(groupVO)));
        PowerMockito.when(rmiClient.getParams(any(), any(), anyMap(), anyMap(), any())).thenReturn(success);
        List<LovValueDTO> lovValueList = new ArrayList<>();
        LovValueDTO lovValueDTO = new LovValueDTO();
        lovValueDTO.setValue("DJI");
        lovValueDTO.setDescription("DJI");
        LovValueDTO lovValueDTO2 = new LovValueDTO();
        lovValueDTO2.setValue("AMFLOW");
        lovValueDTO2.setDescription("AMFLOW");
        lovValueList.add(lovValueDTO);
        lovValueList.add(lovValueDTO2);
        PowerMockito.when(lovAdapter.queryLovValue(any(), any())).thenReturn(lovValueList);
        MessageTemplate messageTemplate = (new MessageTemplate()).setTenantId(0L).setTemplateCode("templateCode")
                .setTemplateTitle("templateTitle").setTemplateContent("setTemplateContentDji").setEnabledFlag(1);
        PowerMockito.when(messageTemplateRepository.selectOne(any())).thenReturn(messageTemplate);
        EmailServer emailServer = (new EmailServer()).setServerCode("sendMailBox").setTenantId(0L).setEnabledFlag(1);
        PowerMockito.when(emailServerRepository.selectOne(any())).thenReturn(emailServer);
        PowerMockito.when(emailRuleConfigRepository.selectByCondition(any())).thenReturn(new ArrayList<>());
        EmailRuleConfig emailRuleConfig = EmailRuleConfig.builder().deleted(false).ruleConfigId("123").build();
        PowerMockito.when(emailRuleConfigRepository.selectByPrimaryKey(any())).thenReturn(emailRuleConfig);

        try {
            EmailRuleConfigUpdateDTO updateDTO = EmailRuleConfigUpdateDTO.builder().ruleConfigId("123").sceneId("sceneId")
                    .groupId(4L).language("zh_CN").sendMailBox("sendMailBox").signName("signName").build();
            String result = emailRuleConfigService.update(updateDTO);
            assertNotNull(result);
        } catch (Exception e) {
            Assert.assertEquals("dji.msg.error.the.content.does.not.match.the.brand", e.getMessage());
        }
    }

    @Test
    public void updateTest() {
        OrderCustomerGroupVO groupVO = new OrderCustomerGroupVO();
        groupVO.setId(4);
        groupVO.setLocked((byte) 0);
        groupVO.setBrand("DJI");
        Result<String> success = Result.success(JSON.toJSONString(Collections.singletonList(groupVO)));
        PowerMockito.when(rmiClient.getParams(any(), any(), anyMap(), anyMap(), any())).thenReturn(success);
        LovValueDTO lovValueDTO = new LovValueDTO();
        lovValueDTO.setValue("DJI");
        lovValueDTO.setDescription("DJI");
        PowerMockito.when(lovAdapter.queryLovValue(any(), any())).thenReturn(Collections.singletonList(lovValueDTO));
        MessageTemplate selectCondition = (new MessageTemplate()).setTenantId(0L).setTemplateCode("templateCode").setTemplateTitle("templateTitle").setEnabledFlag(1);
        PowerMockito.when(messageTemplateRepository.selectOne(any())).thenReturn(selectCondition);
        EmailServer emailServer = (new EmailServer()).setServerCode("sendMailBox").setTenantId(0L).setEnabledFlag(1);
        PowerMockito.when(emailServerRepository.selectOne(any())).thenReturn(emailServer);
        PowerMockito.when(emailRuleConfigRepository.selectByCondition(any())).thenReturn(new ArrayList<>());
        EmailRuleConfig emailRuleConfig = EmailRuleConfig.builder().deleted(false).ruleConfigId("123").build();
        PowerMockito.when(emailRuleConfigRepository.selectByPrimaryKey(any())).thenReturn(emailRuleConfig);
        EmailRuleConfigUpdateDTO updateDTO = EmailRuleConfigUpdateDTO.builder().ruleConfigId("123").sceneId("sceneId")
                .groupId(4L).language("zh_CN").sendMailBox("sendMailBox").build();
        String result = emailRuleConfigService.update(updateDTO);
        assertNotNull(result);
    }

    @Test
    public void removeTest() {
        EmailRuleConfig emailRuleConfig = EmailRuleConfig.builder().deleted(false).ruleConfigId("123").build();
        PowerMockito.when(emailRuleConfigRepository.selectByPrimaryKey(any())).thenReturn(emailRuleConfig);
        Boolean result = emailRuleConfigService.remove("123");
        assertNotNull(result);
    }

    @Test
    public void exportTest() {
        PowerMockito.when(emailRuleConfigRepository.export(any())).thenReturn(new ArrayList<>());
        List<EmailRuleConfigExportDTO> result = emailRuleConfigService.export(EmailRuleConfigQueryDTO.builder().build());
        assertNotNull(result);
    }

    @Test
    public void sendMailGeneralTest_isNull() {
        PowerMockito.when(emailRuleConfigRepository.selectByCondition(any())).thenReturn(Collections.emptyList());
        PowerMockito.when(djiInterfaceFeign.createLog(anyLong(), any())).thenReturn(ResponseEntity.ok(1));
        PowerMockito.when(emailSendService.sendMessage(any())).thenReturn(null);
        try {
            Message result = emailRuleConfigService.sendMailGeneral(SendMailGeneralDTO.builder().sceneId("sceneId")
                    .groupId(4L).language("language").build());
            assertNull(result);
        } catch (Exception e) {
            Assert.assertEquals("dji.msg.error.no.email.send.rules.config", e.getMessage());
        }
    }

    @Test
    public void sendMailGeneralTest_isTwo() {
        EmailRuleConfig emailRuleConfig = EmailRuleConfig.builder().deleted(false).sceneId("sceneId").groupId(4L)
                .language("language").templateCode("templateCode").sendMailBox("sendMailBox").signName("signName")
                .ruleConfigId("123").build();
        EmailRuleConfig emailRuleConfig1 = EmailRuleConfig.builder().deleted(false).sceneId("sceneId").groupId(4L)
                .language("language").templateCode("templateCode").sendMailBox("sendMailBox").signName("signName")
                .ruleConfigId("123").build();
        List<EmailRuleConfig> emailRuleConfigs = new ArrayList<>();
        emailRuleConfigs.add(emailRuleConfig);
        emailRuleConfigs.add(emailRuleConfig1);
        PowerMockito.when(emailRuleConfigRepository.selectByCondition(any())).thenReturn(emailRuleConfigs);
        PowerMockito.when(djiInterfaceFeign.createLog(anyLong(), any())).thenReturn(ResponseEntity.ok(1));
        PowerMockito.when(emailSendService.sendMessage(any())).thenReturn(null);
        try {
            Message result = emailRuleConfigService.sendMailGeneral(SendMailGeneralDTO.builder().sceneId("sceneId")
                    .groupId(4L).language("language").build());
            assertNull(result);
        } catch (Exception e) {
            Assert.assertEquals("dji.msg.error.size.email.send.rules.config", e.getMessage());
        }
    }

    @Test
    public void sendMailGeneralTest() {
        EmailRuleConfig emailRuleConfig = EmailRuleConfig.builder().deleted(false).sceneId("sceneId").groupId(4L)
                .language("language").templateCode("templateCode").sendMailBox("sendMailBox").signName("signName")
                .ruleConfigId("123").build();
        PowerMockito.when(emailRuleConfigRepository.selectByCondition(any())).thenReturn(Collections.singletonList(emailRuleConfig));
        PowerMockito.when(emailSendService.sendMessage(any())).thenReturn(new Message());
        PowerMockito.when(djiInterfaceFeign.createLog(anyLong(), any())).thenReturn(ResponseEntity.ok(1));
        Message result = emailRuleConfigService.sendMailGeneral(SendMailGeneralDTO.builder().sceneId("sceneId")
                .groupId(4L).language("language").build());
        assertNotNull(result);
    }

    @Test
    public void getEmailRuleAndTemplateInfoTest() {
        PowerMockito.when(emailRuleConfigRepository.queryEmailRuleConfigCmList(any())).thenReturn(Collections.singletonList(new EmailConfigTemplateInfoVO()));
        List<EmailConfigTemplateInfoVO> result = emailRuleConfigService.getEmailRuleAndTemplateInfo(Collections.singletonList(EmailRuleConfigCmQueryDTO.builder().build()));
        assertNotNull(result);
    }


}
