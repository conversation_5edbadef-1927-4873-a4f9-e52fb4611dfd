package org.hzero.message.infra.feign.impl;


import io.choerodon.core.exception.*;
import org.hzero.message.api.dto.*;
import org.hzero.message.infra.feign.*;
import org.springframework.http.*;
import org.springframework.stereotype.*;


/**
 * interface feign impl
 *
 * <AUTHOR> 2020/12/29 17:12
 **/
@Component
public class DjiInterfaceFeignFallback implements DjiInterfaceFeign {

    private static final String CALLBACK_MESSAGE = "Interface service is not available, please check";

    @Override
    public ResponseEntity<Integer> createLog(Long organizationId, DjiInterfaceLogDTO interfaceLogDTO) {
        throw new CommonException(CALLBACK_MESSAGE);
    }

}
