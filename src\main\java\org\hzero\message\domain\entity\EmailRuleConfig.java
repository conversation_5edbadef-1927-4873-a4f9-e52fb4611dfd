package org.hzero.message.domain.entity;

import com.fasterxml.jackson.annotation.*;
import io.choerodon.mybatis.annotation.*;
import io.choerodon.mybatis.domain.*;
import io.swagger.annotations.*;
import lombok.*;
import org.hzero.core.cache.Cacheable;
import org.hzero.core.cache.*;
import org.hzero.message.domain.vo.*;
import org.hzero.starter.keyencrypt.core.*;

import javax.persistence.*;
import javax.validation.constraints.*;

/**
 * 邮件规则配置表
 *
 * <AUTHOR> 2024-06-14 15:38:08
 */
@ApiModel("邮件规则配置表")
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@VersionAudit
@ModifyAudit
@JsonInclude(value = JsonInclude.Include.NON_NULL)
@Table(name = "hmsg_email_rule_config")
public class EmailRuleConfig extends AuditDomain implements Cacheable {

    public static final String FIELD_RULE_CONFIG_ID = "ruleConfigId";
    public static final String FIELD_SCENE_ID = "sceneId";
    public static final String FIELD_GROUP_ID = "groupId";
    public static final String FIELD_GROUP_NAME = "groupName";
    public static final String FIELD_GROUP_EN_NAME = "groupEnName";
    public static final String FIELD_LANGUAGE = "language";
    public static final String FIELD_SEND_MAIL_BOX = "sendMailBox";
    public static final String FIELD_SEND_MAIL_BOX_NAME = "sendMailBoxName";
    public static final String FIELD_TEMPLATE_ID = "templateId";
    public static final String FIELD_TEMPLATE_CODE = "templateCode";
    public static final String FIELD_TEMPLATE_NAME = "templateName";
    public static final String FIELD_SIGN_NAME = "signName";
    public static final String FIELD_DELETED = "deleted";

    //
    // 业务方法(按public protected private顺序排列)
    // ------------------------------------------------------------------------------

    //
    // 数据库字段
    // ------------------------------------------------------------------------------


    @ApiModelProperty("id")
    @Id
    @GeneratedValue
    private String ruleConfigId;
    @ApiModelProperty(value = "场景id", required = true)
    @NotBlank
    private String sceneId;
    @ApiModelProperty(value = "组别id", required = true)
    @NotNull
    private Long groupId;
    @ApiModelProperty(value = "组别名称", required = true)
    @NotBlank
    private String groupName;
    @ApiModelProperty(value = "组别名称", required = true)
    @NotBlank
    private String groupEnName;
    @ApiModelProperty(value = "语种", required = true)
    @NotBlank
    private String language;
    @ApiModelProperty(value = "发件邮箱", required = true)
    @NotBlank
    private String sendMailBox;
    @ApiModelProperty(value = "发件邮箱名称", required = true)
    @NotBlank
    private String sendMailBoxName;
    @ApiModelProperty("消息模板ID")
    @NotNull
    private Long templateId;
    @ApiModelProperty(value = "消息模板编码", required = true)
    @NotBlank
    private String templateCode;
    @ApiModelProperty(value = "消息模板名称", required = true)
    @NotBlank
    private String templateName;
    @ApiModelProperty(value = "发件落款")
    private String signName;
    @ApiModelProperty(value = "是否删除(0未删除，1已删除)", required = true)
    @NotNull
    private Boolean deleted;

    //
    // 非数据库字段
    // ------------------------------------------------------------------------------

    //
    // getter/setter
    // ------------------------------------------------------------------------------

    @Transient
    @CacheValue(key = "hiam:user", primaryKey = "createdBy", searchKey = "realName", db = 1,
            structure = CacheValue.DataStructure.MAP_OBJECT)
    private String createdUserName;

    @Transient
    @CacheValue(key = "hiam:user", primaryKey = "lastUpdatedBy", searchKey = "realName", db = 1,
            structure = CacheValue.DataStructure.MAP_OBJECT)
    private String lastUpdatedByName;


}
