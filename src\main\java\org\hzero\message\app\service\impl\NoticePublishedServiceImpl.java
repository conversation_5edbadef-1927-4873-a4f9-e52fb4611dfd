

package org.hzero.message.app.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.choerodon.core.domain.Page;
import io.choerodon.core.oauth.DetailsHelper;
import io.choerodon.mybatis.pagehelper.domain.PageRequest;

import java.util.*;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.hzero.boot.message.entity.Receiver;
import org.hzero.core.base.BaseConstants;
import org.hzero.core.redis.RedisHelper;
import org.hzero.core.util.ResponseUtils;
import org.hzero.message.api.dto.UnitUserDTO;
import org.hzero.message.app.service.NoticePublishedService;
import org.hzero.message.app.service.WebSendService;
import org.hzero.message.domain.entity.Notice;
import org.hzero.message.domain.entity.NoticeContent;
import org.hzero.message.domain.entity.NoticePublished;
import org.hzero.message.domain.entity.NoticeReceiver;
import org.hzero.message.domain.entity.UserGroupAssign;
import org.hzero.message.domain.repository.NoticeContentRepository;
import org.hzero.message.domain.repository.NoticePublishedRepository;
import org.hzero.message.domain.repository.NoticeReceiverRepository;
import org.hzero.message.domain.repository.NoticeRepository;
import org.hzero.message.domain.repository.UserMessageRepository;
import org.hzero.message.domain.vo.UserInfoVO;
import org.hzero.message.infra.feign.IamRemoteService;
import org.hzero.message.infra.feign.UnitService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class NoticePublishedServiceImpl implements NoticePublishedService {
    @Autowired
    private NoticeReceiverRepository noticeReceiverRepository;
    @Autowired
    private NoticePublishedRepository noticePublishedRepository;
    @Autowired
    private NoticeRepository noticeRepository;
    @Autowired
    private WebSendService webSendService;
    @Autowired
    private RedisHelper redisHelper;
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private NoticeContentRepository noticeContentRepository;
    @Autowired
    private IamRemoteService iamRemoteService;
    @Autowired
    private UnitService unitService;
    @Autowired
    private UserMessageRepository userMessageRepository;

    private static final String BULLETIN_SET = "hmsg:published_notice_user_set:";

    public NoticePublishedServiceImpl() {
    }

    @Transactional(
        rollbackFor = {Exception.class}
    )
    public NoticePublished publicNotice(List<Long> publishedIds, Long noticeId, Long organizationId) {
        if (CollectionUtils.isEmpty(publishedIds)) {
            return null;
        } else {
            organizationId = organizationId == null ? DetailsHelper.getUserDetails().getTenantId() : organizationId;
            Notice notice = Notice.updateStatus(this.noticeRepository, noticeId, "PUBLISHED");
            NoticeContent noticeContent = (NoticeContent)this.noticeContentRepository.selectOne((new NoticeContent()).setNoticeId(noticeId));
            List<NoticeReceiver> noticeReceiverList = this.noticeReceiverRepository.listReceiveRecordPage(publishedIds);
            NoticePublished noticePublished = (new NoticePublished()).setNoticeId(noticeId).setPublishedStatusCode("DRAFT").setTenantId(organizationId);
            int count = this.noticePublishedRepository.selectCount(noticePublished);
            if (count > 0) {
                noticePublished = (NoticePublished)this.noticePublishedRepository.selectOne(noticePublished);
                this.noticePublishedRepository.updateByPrimaryKeySelective(noticePublished.setTitle(notice.getTitle()).setReceiverTypeCode(notice.getReceiverTypeCode()).setNoticeBody(noticeContent.getNoticeBody()).setNoticeTypeCode(notice.getNoticeTypeCode()).setPublishedDate(notice.getPublishedDate()).setStartDate(notice.getStartDate()).setPublishedBy(notice.getPublishedBy()).setEndDate(notice.getEndDate()).setAttachmentUuid(notice.getAttachmentUuid()).setNoticeCategoryCode(notice.getNoticeCategoryCode()).setPublishedStatusCode("PUBLISHED"));
            } else {
                this.noticePublishedRepository.insertSelective(noticePublished.setTitle(notice.getTitle()).setReceiverTypeCode(notice.getReceiverTypeCode()).setNoticeBody(noticeContent.getNoticeBody()).setNoticeTypeCode(notice.getNoticeTypeCode()).setPublishedDate(notice.getPublishedDate()).setStartDate(notice.getStartDate()).setPublishedBy(notice.getPublishedBy()).setEndDate(notice.getEndDate()).setAttachmentUuid(notice.getAttachmentUuid()).setNoticeCategoryCode(notice.getNoticeCategoryCode()).setPublishedStatusCode("PUBLISHED"));
            }

            if (publishedIds.size() != 1 || !Objects.equals(publishedIds.get(0), noticePublished.getPublishedId())) {
                this.noticeReceiverRepository.batchDelete(this.noticeReceiverRepository.select((new NoticeReceiver()).setPublishedId(noticePublished.getPublishedId())));
                Iterator var9 = noticeReceiverList.iterator();

                while(var9.hasNext()) {
                    NoticeReceiver item = (NoticeReceiver)var9.next();
                    this.noticeReceiverRepository.insertSelective(item.setPublishedId(noticePublished.getPublishedId()).setTenantId(organizationId));
                }
            }

            if ("NOTIFY".equals(notice.getReceiverTypeCode())) {
                this.sendMessage(notice, noticeReceiverList, noticePublished, organizationId);
            } else {
                notice.refreshCachePublishedNotices(this.redisHelper, this.objectMapper);
                // 将本次需要通知的用户存到redis
                generateBulletinQueueByUser(notice, noticeReceiverList, organizationId);
            }

            return noticePublished;
        }
    }

    private void generateBulletinQueueByUser(Notice notice, List<NoticeReceiver> noticeReceiverList, Long organizationId) {
        if (StringUtils.equals("None", notice.getRemindTypeCode())) {
            if (redisHelper.hasKey(BULLETIN_SET + notice.getNoticeId())){
                redisHelper.delKey(BULLETIN_SET + notice.getNoticeId());
            }
            return;
        }
        List<Receiver> receiverList = generateReceiveUser(notice, noticeReceiverList, organizationId);
        if (CollectionUtils.isEmpty(receiverList)){
            return;
        }
        Set<String> userIdSet = receiverList.stream().map(Receiver::getUserId).map(String::valueOf).collect(Collectors.toSet());
        redisHelper.setAdd(BULLETIN_SET + notice.getNoticeId(),userIdSet.toArray(new String[0]));
    }

    private void sendMessage(Notice notice, List<NoticeReceiver> noticeReceiverList, NoticePublished noticePublished, Long organizationId) {

        List<Receiver> receiverList = generateReceiveUser(notice, noticeReceiverList, organizationId);
        Iterator var8;
        Receiver receiver;

        var8 = ((List)((List)receiverList).stream().distinct().collect(Collectors.toList())).iterator();

        while(var8.hasNext()) {
            receiver = (Receiver)var8.next();
            this.webSendService.saveUserMessage("NOTICE", receiver.getUserId(), noticePublished.getPublishedId(), organizationId, receiver.getTargetUserTenantId(), notice.getTitle());
        }

    }


    /**
     *  获取所有发送人集合
     * @param notice 通知内容
     * @param noticeReceiverList 公告接收记录
     * @param organizationId 组织id
     * @return receiverList
     */
    private List<Receiver> generateReceiveUser(Notice notice, List<NoticeReceiver> noticeReceiverList, Long organizationId) {
        List<UnitUserDTO> unitList = new ArrayList();
        List<UserGroupAssign> userGroupAssignList = new ArrayList();
        List<Receiver> receiverList = new ArrayList();
        Iterator var8 = noticeReceiverList.iterator();

        while(var8.hasNext()) {
            NoticeReceiver noticeReceiver = (NoticeReceiver)var8.next();
            if ("USER".equals(noticeReceiver.getReceiverTypeCode())) {
                ((List)receiverList).add((new Receiver()).setUserId(noticeReceiver.getReceiverSourceId()).setTargetUserTenantId(organizationId));
            } else if ("USER_GROUP".equals(noticeReceiver.getReceiverTypeCode())) {
                UserGroupAssign dto = new UserGroupAssign();
                dto.setUserGroupId(noticeReceiver.getReceiverSourceId());
                dto.setTenantId(noticeReceiver.getTenantId());
                userGroupAssignList.add(dto);
            } else {
                int page;
                short size;
                List userList;
                Iterator var13;
                UserInfoVO userInfo;
                if ("TENANT".equals(noticeReceiver.getReceiverTypeCode())) {
                    page = 0;
                    size = 400;

                    while(true) {
                        userList = ((Page) ResponseUtils.getResponse(this.iamRemoteService.pageUser(noticeReceiver.getReceiverSourceId(), page, Integer.valueOf(size)), new TypeReference<Page<UserInfoVO>>() {
                        })).getContent();
                        ++page;
                        var13 = userList.iterator();

                        while(var13.hasNext()) {
                            userInfo = (UserInfoVO)var13.next();
                            ((List)receiverList).add((new Receiver()).setUserId(userInfo.getId()).setTargetUserTenantId(organizationId));
                        }

                        if (userList.size() != size) {
                            break;
                        }
                    }
                } else if ("UNIT".equals(noticeReceiver.getReceiverTypeCode())) {
                    UnitUserDTO dto = new UnitUserDTO();
                    dto.setUnitId(noticeReceiver.getReceiverSourceId());
                    dto.setTenantId(noticeReceiver.getTenantId());
                    unitList.add(dto);
                } else {
                    if ("ALL".equals(noticeReceiver.getReceiverTypeCode()) && "ANNOUNCE".equals(notice.getReceiverTypeCode())) {
                        receiverList = this.userMessageRepository.getAllUser(organizationId);
                        userGroupAssignList = null;
                        unitList = null;
                        break;
                    }

                    if ("ROLE".equals(noticeReceiver.getReceiverTypeCode())) {
                        page = 0;
                        size = 400;

                        while(true) {
                            userList = ((Page)ResponseUtils.getResponse(this.iamRemoteService.listRoleMembers(BaseConstants.DEFAULT_TENANT_ID, noticeReceiver.getReceiverSourceId(), page, Integer.valueOf(size)), new TypeReference<Page<UserInfoVO>>() {
                            })).getContent();
                            ++page;
                            var13 = userList.iterator();

                            while(var13.hasNext()) {
                                userInfo = (UserInfoVO)var13.next();
                                ((List)receiverList).add((new Receiver()).setUserId(userInfo.getId()).setTargetUserTenantId(organizationId));
                            }

                            if (userList.size() != size) {
                                break;
                            }
                        }
                    }
                }
            }
        }

        Set receiverSet;
        Iterator var16;
        Receiver receiver;
        if (CollectionUtils.isNotEmpty(userGroupAssignList)) {
            receiverSet = (Set)ResponseUtils.getResponse(this.iamRemoteService.listUserGroupAssignUsers(userGroupAssignList), new TypeReference<Set<Receiver>>() {
            });
            if (CollectionUtils.isNotEmpty(receiverSet)) {
                var16 = receiverSet.iterator();

                while(var16.hasNext()) {
                    receiver = (Receiver)var16.next();
                    ((List)receiverList).add((new Receiver()).setUserId(receiver.getUserId()).setTargetUserTenantId(receiver.getTargetUserTenantId()));
                }
            }
        }

        if (CollectionUtils.isNotEmpty(unitList)) {
            receiverSet = (Set)ResponseUtils.getResponse(this.unitService.listUnitUsers(unitList), new TypeReference<Set<Receiver>>() {
            });
            if (CollectionUtils.isNotEmpty(receiverSet)) {
                var16 = receiverSet.iterator();

                while(var16.hasNext()) {
                    receiver = (Receiver)var16.next();
                    ((List)receiverList).add((new Receiver()).setUserId(receiver.getUserId()).setTargetUserTenantId(receiver.getTargetUserTenantId()));
                }
            }
        }
        return receiverList;
    }

    public Page<NoticePublished> listNoticePublished(Long tenantId, PageRequest pageRequest, Long noticeId) {
        return this.noticePublishedRepository.listNoticePublished(tenantId, pageRequest, noticeId);
    }
}
