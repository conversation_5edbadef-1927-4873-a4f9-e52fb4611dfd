package org.hzero.boot.imported.app.service.impl;

import com.fasterxml.jackson.core.type.*;
import com.fasterxml.jackson.databind.*;
import io.choerodon.core.exception.*;
import org.apache.commons.lang3.StringUtils;
import org.hzero.boot.imported.api.dto.*;
import org.hzero.boot.imported.app.service.*;
import org.hzero.boot.imported.config.*;
import org.hzero.boot.imported.domain.entity.*;
import org.hzero.boot.imported.domain.repository.*;
import org.hzero.boot.imported.infra.constant.*;
import org.hzero.boot.imported.infra.enums.*;
import org.hzero.boot.imported.infra.execute.*;
import org.slf4j.*;
import org.springframework.beans.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.core.task.*;
import org.springframework.security.core.context.*;
import org.springframework.stereotype.*;
import org.springframework.util.*;

import java.io.*;
import java.util.*;


/**
 * description
 *
 * <AUTHOR> 2020/12/02 20:10
 */
@Service
public class ImportDataServiceExtImpl implements ImportDataExtService {

    private static final Logger logger = LoggerFactory.getLogger(ImportDataServiceExtImpl.class);
    private final ImportDataRepository importDataRepository;
    private final TemplateClientService templateClientService;
    private final ImportDataRepository dataRepository;
    private final ImportRepository importRepository;
    private final ImportConfig importConfig;
    private final AsyncTaskExecutor taskExecutor;
    private final ObjectMapper objectMapper;

    @Autowired
    public ImportDataServiceExtImpl(ImportDataRepository importDataRepository,
                                 TemplateClientService templateClientService,
                                 ImportDataRepository dataRepository,
                                 ImportRepository importRepository,
                                 ImportConfig importConfig,
                                 @Qualifier("import-executor") AsyncTaskExecutor taskExecutor,
                                 ObjectMapper objectMapper) {
        this.importDataRepository = importDataRepository;
        this.templateClientService = templateClientService;
        this.dataRepository = dataRepository;
        this.importRepository = importRepository;
        this.importConfig = importConfig;
        this.taskExecutor = taskExecutor;
        this.objectMapper = objectMapper;
    }


    @Override
    public Import validateData(Long tenantId, String templateCode, String batch, Map<String, Object> args) {
        Import imported = importRepository.selectOne(new Import().setBatch(batch));
        // 组合自定义参数、校验状态
        prepare(imported, args);
        Assert.isTrue(this.importDataRepository.selectCount((new ImportData()).setBatch(batch).setDataStatus(DataStatus.NEW)) > 0, HimpBootConstants.ErrorCode.DATA_VALIDATE);
        // 更新状态
        importRepository.updateOptional(imported.setStatus(HimpBootConstants.ImportStatus.CHECKING), Import.FIELD_STATUS);
        // 启动数据校验线程
        taskExecutor.execute(new DataValidateExtExecute(
                templateClientService.getTemplate(tenantId, templateCode),
                imported,
                importConfig.getBatchSize(),
                importRepository,
                dataRepository,
                SecurityContextHolder.getContext().getAuthentication(),
                args));
        return imported;
    }


    /**
     * 组合自定义参数、校验状态
     *
     * @param imported 导入对象
     * @param args     参数
     */
    private void prepare(Import imported, Map<String, Object> args) {
        Assert.notNull(imported, HimpBootConstants.ErrorCode.BATCH_NOT_EXISTS);
        if (StringUtils.isNotBlank(imported.getParam())) {
            try {
                // 接口传入与导入文件时指定的参数取并集
                Map<String, Object> map = objectMapper.readValue(imported.getParam(), new TypeReference<Map<String, Object>>() {
                });
                args.putAll(map);
            } catch (IOException e) {
                throw new CommonException(e);
            }
        }
        // 校验状态
        validateStatus(imported.getStatus());
    }

    @Override
    public Import syncValidateData(Long tenantId, String templateCode, String batch, Map<String, Object> args) {
        Import imported = importRepository.selectOne(new Import().setBatch(batch));
        // 组合自定义参数、校验状态
        prepare(imported, args);
        Assert.isTrue(this.importDataRepository.selectCount((new ImportData()).setBatch(batch).setDataStatus(DataStatus.NEW)) > 0, HimpBootConstants.ErrorCode.DATA_VALIDATE);
        // 更新状态
        importRepository.updateOptional(imported.setStatus(HimpBootConstants.ImportStatus.CHECKING), Import.FIELD_STATUS);
        DataValidateExtExecute dataValidate = new DataValidateExtExecute(
                templateClientService.getTemplate(tenantId, templateCode),
                imported,
                importConfig.getBatchSize(),
                importRepository,
                dataRepository,
                SecurityContextHolder.getContext().getAuthentication(),
                args);
        dataValidate.run();
        return imported;
    }

    @Override
    public Import syncValidateDataProduct(Long tenantId, String templateCode, String batch, Map<String, Object> args) {
        Import imported = importRepository.selectOne(new Import().setBatch(batch));
        // 组合自定义参数、校验状态
        prepare(imported, args);
        // 更新状态
        importRepository.updateOptional(imported.setStatus(HimpBootConstants.ImportStatus.CHECKING), Import.FIELD_STATUS);
        DataValidateExtExecute dataValidate = new DataValidateExtExecute(
                templateClientService.getTemplate(tenantId, templateCode),
                imported,
                importConfig.getBatchSize(),
                importRepository,
                dataRepository,
                SecurityContextHolder.getContext().getAuthentication(),
                args);
        dataValidate.run();
        return imported;
    }

    @Override
    public ImportDTO syncImportData(Long tenantId, String templateCode, String batch, Map<String, Object> args) {
        Import imported = (Import) this.importRepository.selectOne((new Import()).setBatch(batch));
        this.prepare(imported, args);
        ImportData importData = this.importDataRepository.selectOne((new ImportData()).setBatch(batch));
        if (Objects.nonNull(importData) && Objects.nonNull(importData.getErrorMsg())) {
            throw new CommonException("数据校验错误：" + importData.getErrorMsg());
        }
        this.importRepository.updateOptional(imported.setStatus("IMPORTING"), new String[]{"status"});
        DataImportExecute dataImport = new DataImportExecute(this.templateClientService.getTemplate(tenantId, templateCode), imported, this.importConfig, this.importRepository, this.dataRepository, SecurityContextHolder.getContext().getAuthentication(), args);
        dataImport.run();
        Import anImport = (Import) this.importRepository.selectOne((new Import()).setBatch(batch));
        if (anImport != null) {
            ImportDTO importDTO = new ImportDTO();
            BeanUtils.copyProperties(anImport, importDTO);
            return importDTO;
        } else {
            return null;
        }
    }


    /**
     * 校验状态
     *
     * @param status 状态
     */
    private void validateStatus(String status) {
        Assert.isTrue(!Objects.equals(status, HimpBootConstants.ImportStatus.UPLOADING), HimpBootConstants.ErrorCode.UPLOADING);
        Assert.isTrue(!Objects.equals(status, HimpBootConstants.ImportStatus.CHECKING), HimpBootConstants.ErrorCode.CHECKING);
        Assert.isTrue(!Objects.equals(status, HimpBootConstants.ImportStatus.IMPORTING), HimpBootConstants.ErrorCode.IMPORTING);
    }
}
