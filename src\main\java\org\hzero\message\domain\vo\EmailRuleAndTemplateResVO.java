

package org.hzero.message.domain.vo;

import io.choerodon.mybatis.domain.*;
import io.swagger.annotations.*;
import lombok.*;
import lombok.experimental.*;
import org.hzero.core.cache.*;
import org.hzero.message.domain.entity.*;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("邮件规则配置及模版内容返回vo")
public class EmailRuleAndTemplateResVO extends AuditDomain implements Cacheable {

    @ApiModelProperty(value = "邮件规则配置")
    private EmailRuleConfigResVO ruleConfigResVO;

    @ApiModelProperty(value = "邮件模版")
    private MessageTemplate messageTemplate;

}
