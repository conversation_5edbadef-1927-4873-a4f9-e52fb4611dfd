package org.hzero.message.infra.feign;

import io.swagger.annotations.ApiOperation;
import org.hzero.boot.message.entity.Attachment;
import org.hzero.message.infra.feign.impl.DjiMarketingFeignFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR> 2021/3/9 16:39
 */
@FeignClient(value = "${hzero.service.marketing.name:o2-marketing-console}", fallbackFactory = DjiMarketingFeignFallback.class)
public interface DjiMarketingFeign {


    /**
     * 获取CM邮件附件
     *
     * @param cmNumber 发送参数
     * @return Message
     */
    @ApiOperation("邮件发送")
    @GetMapping(path = {"/v1/djipm-cm/cm/getSendEmailAttachment"})
    Attachment transformCmPdf(@RequestParam String cmNumber);

    /**
     * 更新Cm状态
     *
     * @param cmPiId 发送参数
     */
    @ApiOperation("更新Cm状态")
    @GetMapping(path = {"/v1/djipm-cm/cm/updateCmStatus"})
    void updateCmStatus(@RequestParam String cmPiId);

    /**
     * 查询Cm状态
     *
     * @param cmNumber 发送参数
     */
    @ApiOperation("查询Cm状态")
    @GetMapping(path = {"/v1/djipm-cm/cm/getCmInfo"})
    String getCmInfoList(@RequestParam String cmNumber);
}
