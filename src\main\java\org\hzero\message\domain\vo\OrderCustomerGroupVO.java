package org.hzero.message.domain.vo;

import com.alibaba.fastjson.annotation.*;
import io.swagger.annotations.*;
import lombok.*;
import lombok.experimental.*;

/**
 * 订单客户组别
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/06/15
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel(value = "OrderCustomerGroupVO", description = "订单客户组别VO")
public class OrderCustomerGroupVO {

    /**
     * 客户组别ID
     */
    private Integer id;

    /**
     * 客户组别名称
     */
    private String name;

    /**
     * 客户组别英文名称
     */
    @JSONField(name = "enname")
    private String enName;

    /**
     * 状态 0有效 1失效
     */
    private Byte locked;

    /**
     * 销售订单编码前缀
     */
    private String piPrefix;

    /**
     * 所属品牌
     */
    private String brand;

    /**
     * 组别logoUrl
     */
    private String logoUrl;

}