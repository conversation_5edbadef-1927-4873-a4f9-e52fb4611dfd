package org.hzero.message.imported;


import com.dji.dava.utils.*;
import com.fasterxml.jackson.databind.*;
import io.choerodon.core.oauth.*;
import lombok.*;
import lombok.extern.slf4j.*;
import org.hzero.boot.imported.app.service.*;
import org.hzero.boot.imported.domain.entity.*;
import org.hzero.boot.imported.infra.validator.annotation.*;
import org.hzero.message.api.dto.*;
import org.hzero.message.app.service.*;
import org.hzero.message.constant.*;
import org.hzero.message.domain.entity.*;
import org.hzero.message.domain.repository.*;
import org.hzero.message.domain.vo.*;
import org.hzero.message.util.*;
import org.hzero.mybatis.domian.*;
import org.hzero.mybatis.util.*;
import org.springframework.transaction.annotation.*;

import java.util.*;
import java.util.function.*;
import java.util.stream.*;

/**
 * <AUTHOR>
 */
@Slf4j
@AllArgsConstructor
@ImportService(templateCode = TemplateConstants.ImportTemplateCode.HMSG_EMAIL_RULE_CONFIG)
public class EmailRuleConfigInputImportService extends BatchImportHandler {

    private final ObjectMapper objectMapper;

    private final EmailRuleConfigService emailRuleConfigService;

    private final EmailServerRepository emailServerRepository;

    private final MessageTemplateRepository messageTemplateRepository;

    private final EmailRuleConfigRepository emailRuleConfigRepository;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean doImport(List<String> data) {
        List<ImportData> importData = this.getContextList();
        List<ValidatorPair<EmailRuleConfigInputDTO>> validatorPairs = DjiImportUtils.convertToValidatorPairs(objectMapper, EmailRuleConfigInputDTO.class, importData);
        List<EmailRuleConfigInputDTO> emailRuleConfigInputList = validatorPairs.stream().map(ValidatorPair::target).collect(Collectors.toList());
        log.info("EmailRuleConfigInputImportService 数量:{}", emailRuleConfigInputList.size());
        Set<EmailRuleConfigInputDTO> setList = new HashSet<>();
        emailRuleConfigInputList.forEach(item -> setList.add(EmailRuleConfigInputDTO.builder()
                .sceneId(item.getSceneId().trim())
                .groupId(item.getGroupId())
                .language(item.getLanguage().trim()).build()));
        List<EmailRuleConfigResImportVO> emailRuleConfigResImportList = emailRuleConfigRepository.queryEmailRuleConfigListImport(setList);
        log.info("emailRuleConfigResImportList数量:{}", emailRuleConfigResImportList.size());
        // 对应组别id是否有效
        List<OrderCustomerGroupVO> customerGroupList = emailRuleConfigService.queryCustomerGroupList();
        Map<Integer, OrderCustomerGroupVO> groupIdToCustomerGroupMap = customerGroupList.stream()
                .collect(Collectors.toMap(OrderCustomerGroupVO::getId, Function.identity(), (v1, v2) -> v1));
        // 发件邮箱状态=启用的记录
        List<String> sendMailBoxList =
                validatorPairs.stream().map(validatorPair -> validatorPair.target().getSendMailBox())
                        .collect(Collectors.toList());
        List<EmailServer> emailServerList = emailServerRepository.selectByCondition(Condition.builder(EmailServer.class).andWhere(Sqls.custom()
                .andIn(EmailServer.FIELD_SERVER_CODE, sendMailBoxList)
                .andEqualTo(EmailServer.FIELD_ENABLED_FLAG, 1)).build());
        Map<String, EmailServer> emailServerMap = emailServerList.stream()
                .collect(Collectors.toMap(EmailServer::getServerCode, Function.identity(), (v1, v2) -> v1));
        // 消息模板中查询是否存在状态=启用的记录
        List<String> templateCodeList = validatorPairs.stream()
                .map(validatorPair -> validatorPair.target().getTemplateCode()).collect(Collectors.toList());
        List<MessageTemplate> messageTemplateList = messageTemplateRepository.selectByCondition(Condition.builder(MessageTemplate.class).andWhere(Sqls.custom()
                .andIn(MessageTemplate.FIELD_TEMPLATE_CODE, templateCodeList)
                .andEqualTo(MessageTemplate.FIELD_ENABLED_FLAG, 1)).build());
        Map<String, MessageTemplate> messageTemplateMap = messageTemplateList.stream()
                .collect(Collectors.toMap(MessageTemplate::getTemplateCode, Function.identity(), (v1, v2) -> v1));
        // 场景id+组别id+语种
        Map<String, EmailRuleConfigResImportVO> emailRuleConfigResMap = emailRuleConfigResImportList.stream()
                .collect(Collectors.toMap(e -> e.getSceneId().toUpperCase() + "-" + e.getGroupId() + "-" + e.getLanguage(), Function.identity(), (v1, v2) -> v1));
        List<EmailRuleConfig> addList = new ArrayList<>();
        List<EmailRuleConfig> updateList = new ArrayList<>();
        for (EmailRuleConfigInputDTO inputDTO : emailRuleConfigInputList) {
            String key = inputDTO.getSceneId().toUpperCase().trim() + "-" + inputDTO.getGroupId() + "-" + inputDTO.getLanguage();
            EmailRuleConfigResImportVO res = emailRuleConfigResMap.get(key);
            OrderCustomerGroupVO customerGroupVO = groupIdToCustomerGroupMap.getOrDefault(inputDTO.getGroupId().intValue(), new OrderCustomerGroupVO());
            EmailServer emailServer = emailServerMap.getOrDefault(inputDTO.getSendMailBox(), new EmailServer());
            MessageTemplate messageTemplate = messageTemplateMap.getOrDefault(inputDTO.getTemplateCode(), new MessageTemplate());
            if (Objects.isNull(res)) {
                // 新增
                EmailRuleConfig emailRuleConfig = buildEmailRuleConfig(inputDTO, UuidUtils.getLongUUID(), customerGroupVO, emailServer, messageTemplate);
                emailRuleConfig.setDeleted(Boolean.FALSE);
                emailRuleConfig.setCreatedBy(DetailsHelper.getUserDetails().getUserId());
                emailRuleConfig.setCreationDate(new Date());
                emailRuleConfig.setObjectVersionNumber(1L);
                addList.add(emailRuleConfig);
            } else {
                // 更新
                EmailRuleConfig emailRuleConfig = buildEmailRuleConfig(inputDTO, res.getRuleConfigId(), customerGroupVO, emailServer, messageTemplate);
                emailRuleConfig.setObjectVersionNumber(res.getObjectVersionNumber());
                updateList.add(emailRuleConfig);
            }
        }
        emailRuleConfigRepository.batchInsertByNative(addList);
        emailRuleConfigRepository.batchUpdateByNative(updateList);
        return Boolean.TRUE;
    }

    private EmailRuleConfig buildEmailRuleConfig(EmailRuleConfigInputDTO inputDTO, String ruleConfigId,
                                                 OrderCustomerGroupVO customerGroupVO, EmailServer emailServer,
                                                 MessageTemplate messageTemplate) {
        EmailRuleConfig emailRuleConfig = EmailRuleConfig.builder().ruleConfigId(ruleConfigId).sceneId(inputDTO.getSceneId().toUpperCase().trim())
                .groupId(customerGroupVO.getId().longValue()).groupName(customerGroupVO.getName()).groupEnName(customerGroupVO.getEnName())
                .sendMailBox(emailServer.getServerCode()).sendMailBoxName(emailServer.getServerName()).templateId(messageTemplate.getTemplateId())
                .templateCode(messageTemplate.getTemplateCode()).templateName(messageTemplate.getTemplateName())
                .signName(inputDTO.getSignName()).language(inputDTO.getLanguage())
                .build();
        emailRuleConfig.setLastUpdatedBy(DetailsHelper.getUserDetails().getUserId());
        emailRuleConfig.setLastUpdateDate(new Date());
        return emailRuleConfig;
    }
}