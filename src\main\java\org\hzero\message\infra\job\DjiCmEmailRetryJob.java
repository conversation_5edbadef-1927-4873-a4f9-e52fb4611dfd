package org.hzero.message.infra.job;

import com.alibaba.fastjson.JSON;
import io.choerodon.core.exception.CommonException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.hzero.boot.message.entity.Attachment;
import org.hzero.boot.message.entity.MessageSender;
import org.hzero.boot.message.entity.Receiver;
import org.hzero.boot.platform.lov.adapter.LovAdapter;
import org.hzero.boot.platform.lov.dto.LovValueDTO;
import org.hzero.boot.scheduler.infra.annotation.JobHandler;
import org.hzero.boot.scheduler.infra.enums.ReturnT;
import org.hzero.boot.scheduler.infra.handler.IJobHandler;
import org.hzero.boot.scheduler.infra.tool.SchedulerTool;
import org.hzero.message.api.dto.UserMessageDTO;
import org.hzero.message.app.service.EmailSendService;
import org.hzero.message.app.service.GtSendService;
import org.hzero.message.domain.entity.Message;
import org.hzero.message.domain.entity.MessageReceiver;
import org.hzero.message.infra.feign.DjiMarketingFeign;
import org.hzero.message.infra.mapper.MessageManualMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

@JobHandler("djiCmEmailRetryJob")
public class DjiCmEmailRetryJob implements IJobHandler {

    private static final Logger logger = LoggerFactory.getLogger(DjiCmEmailRetryJob.class);

    public static final String CC = "cc";
    public static final String BCC = "bcc";
    public static final String CMNUMBER = "cmNumber";
    public static final String RECEIVEMAILBOX = "receiveMailbox";
    public static final String CM_EMAIL_FAIL_MEASSGE = "DJICM.CM_EMAIL_FAIL_MEASSGE";
    private static final String EMAIL_STATUS = "toBeSent";
    @Autowired
    private LovAdapter lovAdapter;
    @Autowired
    private GtSendService sendService;
    @Autowired
    private EmailSendService emailSendService;
    @Autowired
    private MessageManualMapper messageManualMapper;
    @Autowired
    private DjiMarketingFeign djiMarketingFeign;



    @Override
    @SuppressWarnings("all")
    public ReturnT execute(Map<String, String> map, SchedulerTool tool) {

        // 获取失败的CM邮件
        List<UserMessageDTO> messageInfo = messageManualMapper.getMessageInfo();

        List<String> cmNumbers = new ArrayList<>();
        List<String> successCmNumbers = new ArrayList<>();
        if (CollectionUtils.isEmpty(messageInfo)){
            return ReturnT.SUCCESS;
        }
        logger.info("==========CM邮件重试定时任务开始==========");
        messageInfo.forEach(m->{
            String arg = m.getSendArgs();
            if (!ObjectUtils.isEmpty(arg)){
                Map<String, Object> args = JSON.parseObject(arg, HashMap.class);
                Object cc = args.get(CC);
                Object bcc = args.get(BCC);
                if (!ObjectUtils.isEmpty(cc)){
                    List<String> ccs = Arrays.asList(String.valueOf(cc).split(";"));
                    m.setCcList(ccs);
                }
                if (!ObjectUtils.isEmpty(bcc)){
                    List<String> bccs = Arrays.asList(String.valueOf(bcc).split(";"));
                    m.setBccList(bccs);
                }
                Object cmNumber = args.get(CMNUMBER);
                if (!ObjectUtils.isEmpty(cmNumber)){
                    //判断cm状态不等于待发送，就跳过该条数据
                    String cmStatus = djiMarketingFeign.getCmInfoList(String.valueOf(cmNumber));
                    logger.info("cm邮件重试查询cm单{}状态{}",cmNumber,cmStatus);
                    if (StringUtils.isNotBlank(cmStatus)){
                        if (!cmStatus.equals(EMAIL_STATUS)){
                            return;
                        }
                    }
                    //转换PDF
                    Attachment attachment = djiMarketingFeign.transformCmPdf(String.valueOf(cmNumber));
                    List<Attachment> attachments = new ArrayList<>();
                    attachments.add(attachment);

                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(attachments)){
                        m.setAttachmentList(attachments);
                    }
                }
                Object receiveMailbox = args.get(RECEIVEMAILBOX);
                if (!ObjectUtils.isEmpty(receiveMailbox)){
                    List<String> receiveMailboxList = Arrays.asList(String.valueOf(receiveMailbox).split(";"));
                    m.setMessageReceiverList(getReceiverList(receiveMailboxList));
                }
                int count = 0;
                try {
                    for (int i = 0; i < 3; i++) {
                        Message message = emailSendService.resendMessage(m);
                        if (message.getSendFlag().equals(1)){
                            successCmNumbers.add(String.valueOf(cmNumber));
                            break;
                        }
                        count = count + 1;
                    }
                }catch (Exception e){
                    count = count + 1;
                }
                if (count == 3){
                    logger.info("CM单：{} 邮件重试返回结果",cmNumbers);
                    List<String> cmNumberList = Arrays.asList(String.valueOf(cmNumber).split(";"));
                    cmNumbers.addAll(cmNumberList);
                }
            }
        });
        if (CollectionUtils.isNotEmpty(successCmNumbers)){
            djiMarketingFeign.updateCmStatus(StringUtils.join(successCmNumbers,";"));
            logger.info("CM邮件重试，邮件发送成功单据变更状态：{}",StringUtils.join(successCmNumbers,";"));
        }
        //发送GT消息通知
        if (CollectionUtils.isNotEmpty(cmNumbers)){
            this.sendGt(StringUtils.join(cmNumbers,";"));
            logger.info("CM单：{} 邮件重试3次失败，发送GT消息",cmNumbers);
        }
        logger.info("==========CM邮件重试定时任务结束==========");

        return ReturnT.SUCCESS;
    }

    public void sendGt(String cmNumbers) {
        List<LovValueDTO> gtSendMailBoxValues = lovAdapter.queryLovValue(CM_EMAIL_FAIL_MEASSGE, 0L);
        List<String> values = gtSendMailBoxValues.stream().map(LovValueDTO::getValue).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(values)){
            throw new CommonException("cm email fail GT getValues error.");
        }
        List<Receiver> receivers = getReceivers(values);
        MessageSender messageSender = new MessageSender();
        messageSender.setLang("zh_CN");
        messageSender.setReceiverAddressList(receivers);
        messageSender.setTenantId(0L);
        messageSender.setServerCode("HZERO");
        String message = "CM自动发送邮件失败：您好,CM单【" + cmNumbers + "】自动发送邮件失败，请前往系统查看失败原因，联系管理员处理；";
        org.hzero.boot.message.entity.Message msg=new org.hzero.boot.message.entity.Message();
        msg.setContent(message);
        String subject = "CM发送邮件重试失败";
        msg.setSubject(subject);
        messageSender.setMessage(msg);
        messageSender.setMessageCode("DJIMKT.SENDGT.MESSAGE");
        logger.info("mkt send gt param is:{}", messageSender);
        Message messageResult = sendService.sendMessage(messageSender);
        logger.info("mkt send gt result is:{}", messageResult);
    }

    private List<Receiver> getReceivers(List<String> sendTos) {
        List<Receiver> receivers = new ArrayList<>();
        sendTos.forEach(sendTo -> {
            Receiver receiver = new Receiver();
            // 邮箱塞入ad账号，转换逻辑交给消息服务处理
            receiver.setEmail(sendTo);
            receivers.add(receiver);
        });
        return receivers;
    }

    private List<MessageReceiver> getReceiverList(List<String> sendTos) {
        List<MessageReceiver> receivers = new ArrayList<>();
        sendTos.forEach(sendTo -> {
            MessageReceiver receiver = new MessageReceiver();
            // 邮箱塞入ad账号，转换逻辑交给消息服务处理
            receiver.setReceiverAddress(sendTo);
            receivers.add(receiver);
        });
        return receivers;
    }
}
