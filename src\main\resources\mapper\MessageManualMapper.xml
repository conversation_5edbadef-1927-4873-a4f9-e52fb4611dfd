<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.hzero.message.infra.mapper.MessageManualMapper">
    <select id="getMessageInfo" resultType="org.hzero.message.api.dto.UserMessageDTO">
        select
        hmt.transaction_id,
        hmt.trx_status_code,
        hmt.tenant_id from_tenant_id,
        hmt.object_version_number as transactionObjectVersionNumber,
        hm.message_id,
        hm.tenant_id,
        hm.message_type_code,
        hm.template_code,
        hm.lang,
        hm.server_code,
        hm.subject,
        hm.content,
        hm.send_args,
        hm.send_flag,
        hm.external_code,
        hm.object_version_number,
        hmc.receiver_address,
        hmc.tenant_id target_tenant_id,
        hmc.idd
        from hmsg_message_transaction hmt
        inner join hmsg_message hm on hm.message_id = hmt.message_id
        left join hmsg_message_receiver hmc on hmc.message_id = hm.message_id
        where hm.template_code = 'DJISM.FEAD_BACK_CODE_EMAIL' and hm.send_flag = 0
        and hmt.trx_status_code = 'F'
        AND ( hmc.filter_flag IS NULL OR hmc.filter_flag = 0 )
    </select>
</mapper>