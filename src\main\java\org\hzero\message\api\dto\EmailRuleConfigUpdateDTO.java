

package org.hzero.message.api.dto;

import io.swagger.annotations.*;
import lombok.*;
import lombok.experimental.*;

import javax.validation.constraints.*;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("邮件规则配置更新DTO")
public class EmailRuleConfigUpdateDTO {

    @ApiModelProperty(value = "规则配置id")
    @NotBlank(message = "规则配置id不能为空")
    private String ruleConfigId;

    @ApiModelProperty(value = "场景id")
    @NotBlank(message = "场景id不能为空")
    private String sceneId;

    @ApiModelProperty(value = "组别id")
    @NotNull(message = "组别id不能为空")
    private Long groupId;

    @ApiModelProperty(value = "语种")
    @NotBlank(message = "语种不能为空")
    private String language;

    @ApiModelProperty(value = "发件邮箱")
    @NotBlank(message = "发件邮箱不能为空")
    private String sendMailBox;

    @ApiModelProperty(value = "消息模板编码")
    @NotBlank(message = "消息模板编码不能为空")
    private String templateCode;

    @ApiModelProperty(value = "发件落款")
    private String signName;

}
