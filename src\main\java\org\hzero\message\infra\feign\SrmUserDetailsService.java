package org.hzero.message.infra.feign;

import cn.hutool.system.UserInfo;
import org.hzero.message.infra.feign.impl.SrmUserDetailsServiceImpl;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;


/**
 * 调用iam 查询某个用户的详细信息
 * <AUTHOR>
 * @Date 2020/1/19 16:05
 */
@FeignClient(
        value = "${hzero.service.iam.name:hzero-iam}",
        path = "/hzero/v1/users",
        fallback = SrmUserDetailsServiceImpl.class
)
public interface SrmUserDetailsService {

    @GetMapping({"/{userId}/info"})
    ResponseEntity<UserInfo> queryUserInfoByUserId(@PathVariable("userId") Long userId);

}
