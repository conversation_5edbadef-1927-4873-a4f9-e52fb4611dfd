package org.hzero.message.app.service;

import org.hzero.boot.message.entity.MessageSender;
import org.hzero.message.api.dto.UserMessageDTO;
import org.hzero.message.domain.entity.Message;

public interface EmailSendService {
    Message sendMessage(MessageSender messageSender);

    Message sendMessage(MessageSender messageSender, Integer tryTimes);

    void asyncSendMessage(MessageSender messageSender, Integer tryTimes);

    Message resendMessage(UserMessageDTO message);
}