package org.hzero.message.entity;

import org.hzero.boot.message.entity.MessageSender;
import org.hzero.boot.message.entity.Receiver;
import org.hzero.message.domain.entity.Message;

import java.util.List;
import java.util.Map;


public class MlMessageSender {
    /*
     * key：语言，value :message
     */
    private Map<String, Message> mlMessageMap;

    /*
     * key：语言，value : 偏好设置该语言对应的接收者集合
     */
    private Map<String, List<Receiver>> mlReceiverMap;

    private MessageSender messageSender;


    public MessageSender getMessageSender() {
        return messageSender;
    }

    public MlMessageSender setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
        return this;
    }

    public Map<String, Message> getMlMessageMap() {
        return mlMessageMap;
    }

    public MlMessageSender setMlMessageMap(Map<String, Message> mlMessageMap) {
        this.mlMessageMap = mlMessageMap;
        return this;
    }

    public Map<String, List<Receiver>> getMlReceiverMap() {
        return mlReceiverMap;
    }

    public MlMessageSender setMlReceiverMap(Map<String, List<Receiver>> mlReceiverMap) {
        this.mlReceiverMap = mlReceiverMap;
        return this;
    }
}
