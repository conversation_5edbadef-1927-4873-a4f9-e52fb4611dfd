

package org.hzero.message.api.dto;

import io.swagger.annotations.*;
import lombok.*;
import lombok.experimental.*;
import org.hzero.boot.platform.lov.annotation.*;
import org.hzero.core.cache.*;
import org.hzero.export.annotation.*;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
@ExcelSheet(zh = "邮件发送规则_%s")
@ApiModel(value = "EmailRuleConfigExportDTO", description = "导出邮件规则配置DTO")
public class EmailRuleConfigExportDTO implements Cacheable {

    @ExcelColumn(zh = "场景id")
    private String sceneId;

    @ExcelColumn(zh = "组别id")
    private Long groupId;

    @ExcelColumn(zh = "组别名称")
    private String groupName;

    @LovValue(value = "DJI_LANGUAGE")
    private String language;

    @ExcelColumn(zh = "语种")
    private String languageMeaning;

    @ExcelColumn(zh = "发件邮箱编码")
    private String sendMailBox;

    @ExcelColumn(zh = "发件邮箱名称")
    private String sendMailBoxName;

    @ExcelColumn(zh = "消息模板编码")
    private String templateCode;

    @ExcelColumn(zh = "消息模板名称")
    private String templateName;

    @ExcelColumn(zh = "发件落款")
    private String signName;

}
