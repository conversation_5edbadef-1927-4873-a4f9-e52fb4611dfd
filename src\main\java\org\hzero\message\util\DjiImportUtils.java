package org.hzero.message.util;

import com.fasterxml.jackson.databind.*;
import io.choerodon.core.exception.*;
import lombok.extern.slf4j.*;
import org.apache.commons.collections.*;
import org.hzero.boot.imported.domain.entity.*;
import org.hzero.boot.imported.infra.enums.*;
import org.hzero.core.helper.*;
import org.hzero.core.message.*;
import org.hzero.message.constant.*;

import java.io.*;
import java.util.*;

/**
 * 导入校验工具类
 *
 * <AUTHOR> 2021/3/23 10:29
 **/
@Slf4j
public class DjiImportUtils {

    private DjiImportUtils() {
    }

    /**
     * 追加错误信息
     *
     * @param validatorPair 入参
     */
    public static <T> void appendErrorMsg(ValidatorPair<T> validatorPair, String errorMsg) {
        validatorPair.source().setDataStatus(DataStatus.VALID_FAILED);
        if (Objects.isNull(validatorPair.source().getErrorMsg())) {
            validatorPair.source().setErrorMsg(errorMsg);
        } else if (!validatorPair.source().getErrorMsg().contains(errorMsg)) {
            validatorPair.source().setErrorMsg(validatorPair.source().getErrorMsg() + errorMsg);
        }
    }

    /**
     * 处理校验失败的逻辑
     *
     * @param importData  导入的对象
     * @param messageCode 错误信息编码
     */
    public static void processValidateFailed(ImportData importData, String messageCode) {
        importData.setDataStatus(DataStatus.VALID_FAILED);
        importData.setErrorMsg(MessageAccessor.getMessage(messageCode, LanguageHelper.locale()).desc());
    }

    /**
     * 把导入对象转成 {@link ValidatorPair}
     *
     * @param objectMapper json处理器
     * @param targetClass  目标类型
     * @param dataList     导入对象
     * @param <T>          目标类型的泛型
     * @return {@link ValidatorPair}
     */
    public static <T> List<ValidatorPair<T>> convertToValidatorPairs(ObjectMapper objectMapper, Class<T> targetClass,
                                                                     List<ImportData> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return Collections.emptyList();
        }
        List<ValidatorPair<T>> validatorPairs = new ArrayList<>(dataList.size());

        for (ImportData importData : dataList) {
            T t = null;

            try {
                t = objectMapper.readValue(importData.getData(), targetClass);
            } catch (IOException e) {
                log.error("Convert error", e);
                importData.setDataStatus(DataStatus.VALID_FAILED);
                importData.setErrorMsg(MsgRetConstant.ERROR_DATA_VALID);
            }

            if (t != null) {
                validatorPairs.add(ValidatorPair.of(t, importData));
            }
        }

        return validatorPairs;
    }

    public static <T> List<T> parseList(ObjectMapper objectMapper, Class<T> targetClass, List<String> data) {
        List<T> targetList = new ArrayList<>(data.size());

        for (String item : data) {
            try {
                targetList.add(objectMapper.readValue(item, targetClass));
            } catch (IOException e) {
                log.error("Convert error: ", e);
                throw new CommonException(e);
            }
        }

        return targetList;
    }
}
