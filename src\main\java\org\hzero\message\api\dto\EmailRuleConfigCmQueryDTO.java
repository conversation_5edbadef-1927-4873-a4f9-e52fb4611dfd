

package org.hzero.message.api.dto;

import io.swagger.annotations.*;
import lombok.*;
import lombok.experimental.*;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("邮件规则配置查询CMm侧列表DTO")
public class EmailRuleConfigCmQueryDTO {

    @ApiModelProperty(value = "场景id")
    private String sceneId;

    @ApiModelProperty(value = "组别id")
    private Long groupId;

    @ApiModelProperty(value = "语种")
    private String language;

}
