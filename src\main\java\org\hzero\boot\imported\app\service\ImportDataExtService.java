package org.hzero.boot.imported.app.service;


import org.hzero.boot.imported.api.dto.*;
import org.hzero.boot.imported.domain.entity.*;

import java.util.*;

/**
 * 扩展通用导入服务
 *
 * <AUTHOR> 2020/12/02 19:50
 */
public interface ImportDataExtService {
    /**
     *  批量校验
     *
     * @param tenantId
     * @param templateCode
     * @param batch
     * @param args
     * <AUTHOR> 2020-12-02 19:54
     * @return
     */
    Import validateData(Long tenantId, String templateCode, String batch, Map<String, Object> args);

    /**
     * 校验临时表数据(同步)
     *
     * @param tenantId     租户Id
     * @param templateCode 模板编码
     * @param batch        批次号
     * @param args         自定义参数
     * @return 状态信息
     */
    Import syncValidateData(Long tenantId, String templateCode, String batch, Map<String, Object> args);
    /**
     *
     * 校验临时表数据(同步)-客制化
     * @param tenantId     租户Id
     * @param templateCode 模板编码
     * @param batch        批次号
     * @param args         自定义参数
     * @return org.hzero.boot.imported.domain.entity.Impor
     */
    Import syncValidateDataProduct(Long tenantId, String templateCode, String batch, Map<String, Object> args);
    /**
     *
     * 商品组合商品列表导入
     * <AUTHOR>
     * @param tenantId
     * @param templateCode
     * @param batch
     * @param args
     * @return org.hzero.boot.imported.api.dto.ImportDTO
     */
    ImportDTO syncImportData(Long tenantId, String templateCode, String batch, Map<String, Object> args);
}
