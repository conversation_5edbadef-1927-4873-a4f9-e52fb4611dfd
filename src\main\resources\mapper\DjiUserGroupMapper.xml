<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="org.hzero.message.infra.mapper.DjiUserGroupMapper">
    <select id="selectUserGroupList" resultType="org.hzero.boot.message.entity.Receiver" parameterType="string">
        SELECT
        t3.login_name as email,
        t1.user_id
        FROM
        hzero_platform.hiam_user_group_assign t1
        JOIN hzero_platform.hiam_user_group t2 ON t1.user_group_id = t2.user_group_id
        JOIN hzero_platform.iam_user t3 ON t1.user_id = t3.id
        WHERE
        t2.group_code = #{groupCode}
    </select>
</mapper>