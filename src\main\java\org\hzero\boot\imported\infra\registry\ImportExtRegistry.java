package org.hzero.boot.imported.infra.registry;

import org.apache.commons.lang3.StringUtils;
import org.hzero.boot.imported.app.service.*;
import org.springframework.util.*;

import java.util.*;
import java.util.concurrent.*;

/**
 * 校验注册类
 *
 * <AUTHOR> 2020/12/03 15:39
 */
public class ImportExtRegistry {

    private ImportExtRegistry() {}

    private static final String NAME_PREFIX = "sheetName";

    /**
     * 自定义导入逻辑缓存map,启动时初始化(下标匹配)
     */
    private static final Map<List<Object>, Object> SERVICE_MAP = new ConcurrentHashMap<>();
    /**
     * 自定义校验链缓存map,启动时初始化
     */
    private static final Map<List<Object>, List<BatchValidatorHandler>> VALIDATOR_MAP = new ConcurrentHashMap<>();

    private static final Map<List<Object>, List<AbstractFullValidatorHandler>> FULL_VALIDATOR_MAP =
                    new ConcurrentHashMap();

    public static Object getDefaultServiceMap(String templateCode, int sheetIndex, String sheetName) {
        // 根据sheet页名称匹配默认
        List<Object> key = Arrays.asList(templateCode, StringUtils.EMPTY, NAME_PREFIX + sheetName);
        if (SERVICE_MAP.containsKey(key)) {
            return SERVICE_MAP.get(key);
        }
        // 根据下标匹配默认
        key = Arrays.asList(templateCode, StringUtils.EMPTY, sheetIndex);
        if (SERVICE_MAP.containsKey(key)) {
            return SERVICE_MAP.get(key);
        }
        return Collections.emptyList();
    }

    public static Object getServiceMap(String templateCode, String tenantNum, int sheetIndex, String sheetName) {
        // 先根据sheet页名称匹配
        List<Object> key = Arrays.asList(templateCode, tenantNum, NAME_PREFIX + sheetName);
        if (SERVICE_MAP.containsKey(key)) {
            return SERVICE_MAP.get(key);
        }
        // 根据下标匹配
        key = Arrays.asList(templateCode, tenantNum, sheetIndex);
        if (SERVICE_MAP.containsKey(key)) {
            return SERVICE_MAP.get(key);
        }
        return getDefaultServiceMap(templateCode, sheetIndex, sheetName);
    }

    public static void setServiceMap(String templateCode, String tenantNum, int sheetIndex, String sheetName,
                    Object importService) {
        List<Object> key;
        if (StringUtils.isNotBlank(sheetName)) {
            // 拼个前缀，避免sheet名称是数字
            key = Arrays.asList(templateCode, tenantNum, NAME_PREFIX + sheetName);
        } else {
            key = Arrays.asList(templateCode, tenantNum, sheetIndex);
        }
        if (!(SERVICE_MAP.containsKey(key) && SERVICE_MAP.get(key) instanceof IBatchImportService)) {
            SERVICE_MAP.put(key, importService);
        }
    }


    public static List<BatchValidatorHandler> getDefaultValidatorList(String templateCode, int sheetIndex,
                    String sheetName) {
        // 根据sheet页名称匹配默认
        List<Object> key = Arrays.asList(templateCode, StringUtils.EMPTY, NAME_PREFIX + sheetName);
        if (VALIDATOR_MAP.containsKey(key)) {
            return VALIDATOR_MAP.get(key);
        }
        // 根据下标匹配默认
        key = Arrays.asList(templateCode, StringUtils.EMPTY, sheetIndex);
        if (VALIDATOR_MAP.containsKey(key)) {
            return VALIDATOR_MAP.get(key);
        }
        return Collections.emptyList();
    }

    public static List<BatchValidatorHandler> getValidatorList(String templateCode, String tenantNum, int sheetIndex,
                    String sheetName) {
        // 先根据sheet页名称匹配
        List<Object> key = Arrays.asList(templateCode, tenantNum, NAME_PREFIX + sheetName);
        if (VALIDATOR_MAP.containsKey(key)) {
            return VALIDATOR_MAP.get(key);
        }
        // 根据下标匹配
        key = Arrays.asList(templateCode, tenantNum, sheetIndex);
        if (VALIDATOR_MAP.containsKey(key)) {
            return VALIDATOR_MAP.get(key);
        }
        return getDefaultValidatorList(templateCode, sheetIndex, sheetName);
    }

    public static void addValidator(String templateCode, String tenantNum, int sheetIndex, String sheetName,
                    BatchValidatorHandler validator) {
        List<Object> key;
        if (StringUtils.isNotBlank(sheetName)) {
            // 拼个前缀，避免sheet名称是数字
            key = Arrays.asList(templateCode, tenantNum, NAME_PREFIX + sheetName);
        } else {
            key = Arrays.asList(templateCode, tenantNum, sheetIndex);
        }
        List<BatchValidatorHandler> indexValidatorList = VALIDATOR_MAP.get(key);
        if (CollectionUtils.isEmpty(indexValidatorList)) {
            indexValidatorList = new ArrayList<>();
        }
        indexValidatorList.add(validator);
        VALIDATOR_MAP.put(key, indexValidatorList);
    }

    public static List<AbstractFullValidatorHandler> getDefaultFullValidatorList(String templateCode, int sheetIndex,
                    String sheetName) {
        // 根据sheet页名称匹配默认
        List<Object> key = Arrays.asList(templateCode, StringUtils.EMPTY, NAME_PREFIX + sheetName);
        if (FULL_VALIDATOR_MAP.containsKey(key)) {
            return FULL_VALIDATOR_MAP.get(key);
        }
        // 根据下标匹配默认
        key = Arrays.asList(templateCode, StringUtils.EMPTY, sheetIndex);
        if (FULL_VALIDATOR_MAP.containsKey(key)) {
            return FULL_VALIDATOR_MAP.get(key);
        }
        return Collections.emptyList();
    }

    public static List<AbstractFullValidatorHandler> getFullValidatorList(String templateCode, String tenantNum,
                    int sheetIndex, String sheetName) {
        // 先根据sheet页名称匹配
        List<Object> key = Arrays.asList(templateCode, tenantNum, NAME_PREFIX + sheetName);
        if (FULL_VALIDATOR_MAP.containsKey(key)) {
            return FULL_VALIDATOR_MAP.get(key);
        }
        // 根据下标匹配
        key = Arrays.asList(templateCode, tenantNum, sheetIndex);
        if (FULL_VALIDATOR_MAP.containsKey(key)) {
            return FULL_VALIDATOR_MAP.get(key);
        }
        return getDefaultFullValidatorList(templateCode, sheetIndex, sheetName);
    }

    public static void addFullValidator(String templateCode, String tenantNum, int sheetIndex, String sheetName,
                    AbstractFullValidatorHandler fullValidatorHandler) {
        List<Object> key;
        if (StringUtils.isNotBlank(sheetName)) {
            // 拼个前缀，避免sheet名称是数字
            key = Arrays.asList(templateCode, tenantNum, NAME_PREFIX + sheetName);
        } else {
            key = Arrays.asList(templateCode, tenantNum, sheetIndex);
        }
        List<AbstractFullValidatorHandler> indexValidatorList = FULL_VALIDATOR_MAP.get(key);
        if (CollectionUtils.isEmpty(indexValidatorList)) {
            indexValidatorList = new ArrayList<>();
        }
        indexValidatorList.add(fullValidatorHandler);
        FULL_VALIDATOR_MAP.put(key, indexValidatorList);
    }

}
