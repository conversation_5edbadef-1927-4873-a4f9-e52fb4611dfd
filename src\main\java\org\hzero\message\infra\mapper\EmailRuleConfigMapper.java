package org.hzero.message.infra.mapper;

import io.choerodon.mybatis.common.*;
import org.apache.ibatis.annotations.*;
import org.hzero.message.api.dto.*;
import org.hzero.message.domain.entity.*;
import org.hzero.message.domain.vo.*;

import java.util.*;

/**
 * 邮件规则配置表Mapper
 *
 * <AUTHOR> 2024-06-14 15:38:08
 */
public interface EmailRuleConfigMapper extends BaseMapper<EmailRuleConfig> {

    /**
     * 获取邮件规则配置列表
     * @param queryDTO 查询条件
     * @return 结果
     */
    List<EmailRuleConfigResVO> queryEmailRuleConfigListPage(EmailRuleConfigQueryDTO queryDTO);

    /**
     * 导出邮件规则配置列表
     * @param queryDTO 查询条件
     * @return 结果
     */
    List<EmailRuleConfigExportDTO> export(EmailRuleConfigQueryDTO queryDTO);

    /**
     * 导入查询数据
     * @param setList 入参
     * @return 返参
     */
    List<EmailRuleConfigResImportVO> queryEmailRuleConfigListImport(@Param("list") Set<EmailRuleConfigInputDTO> setList);

    /**
     * 批量插入
     * @param list 订单行
     */
    void batchInsertByNative(@Param("list") List<EmailRuleConfig> list);

    /**
     * 批量更新
     * @param list 订单行
     */
    void batchUpdateByNative(@Param("list") List<EmailRuleConfig> list);

    List<EmailConfigTemplateInfoVO> queryEmailRuleConfigCmList(@Param("list") List<EmailRuleConfigCmQueryDTO> list);
}
