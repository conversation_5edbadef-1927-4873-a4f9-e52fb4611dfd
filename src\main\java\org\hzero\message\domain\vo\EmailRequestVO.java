package org.hzero.message.domain.vo;

import lombok.Data;
import org.hzero.message.api.controller.v1.dto.EmailAttachmentDTO;

import java.util.List;

/**
 * 邮件发送参数
 *
 * <AUTHOR>
 */
@Data
public class EmailRequestVO {
    /**
     * dji-ums的portal后台的应用ID
     */
    private String appId;
    /**
     * 业务ID，建议唯一，方便排查问题
     */
    private String bizId;
    /**
     * 通常是公共帐号，不指定的情况下默认为ums-msg，即ums<EMAIL>
     */
    private String sender;
    /**
     * 密送，可以填AD（当目标邮箱是**********时）或者填完整邮箱
     */
    private List<String> bcc;
    /**
     * 抄送，可以填AD（当目标邮箱是**********时）或者填完整邮箱
     */
    private List<String> cc;
    /**
     * 收件人，可以填AD或者填完整邮箱
     */
    private List<String> recipients;
    /**
     * 收件内容
     */
    private String content;
    /**
     * 邮件标题
     */
    private String title;

    /**
     * 附件列表
     */
    private List<EmailAttachmentDTO> attachList;
}
