# 模板引入，这里不需要动
include:
  - project: 'gitlab/pipeline-template'
    ref: master
    file: '/import.yml'

# stage名称不允许修改，相关规范参考
# https://doc.djicorp.com/view/3d34108e-6f71-43a9-9cb4-bb481f21913b
stages:
  # 分支扫描流水线、开发集成流水线、系统测试流水线、验收测试流水线、主干集成流水线必须包含
  - static-test
  # 分支扫描流水线、开发集成流水线、系统测试流水线、验收测试流水线、主干集成流水线必须包含
  - sonar-test
  # 开发集成流水线、系统测试流水线、验收测试流水线必须包含
  - build
  # 系统测试流水线、验收测试流水线、发布上线流水线必须包含
  - deploy
  # 系统测试流水线、验收测试流水线、发布上线流水线必须包含（都是后端必须包含，前端不需要）
  - integration-test
  # 检入、集成、测试、验收流水线新增代码质量门禁quality-gate节点
  - post-task


#Variables，从这里开始，是需要按工程需要配置的##
variables:
  PROJECT_NAME: "o2-basic-message-be"
  SONARQUBE_PROJECT_NAME: $PROJECT_NAME
  # 部署时的英文名称
  PAMS_APPLICATION_EN_NAME: $PROJECT_NAME
  # 部署时的英文名称
  CI_PROJECT_NAME: $PROJECT_NAME
  MAVEN_OPTS: "-Dmaven.repo.local=.m2"
  JACOCO_PLUGIN: "org.jacoco:jacoco-maven-plugin:0.8.6:prepare-agent"
  MVN_IMAGE: "harbor.djicorp.com/library/maven:3-jdk-8-slim-20201230153015"
  # 适用sonarquber获取单元测试每个子模块的覆盖率，这里填上工程每个模板的名称+/target/,如下
  APP_TARGET: "target/"
  CORE_TARGET: "target/"
  # 这里是固定的，无需修改
  TARGET: "target/"

  # 敏感参数需要在项目Web前端Settings => CI/CD => Variables 中配置
  # harbor配置
  # CI_REGISTRY: # gitlab web前端配置
  # CI_REGISTRY_USER: # gitlab web前端配置
  # CI_REGISTRY_PASSWORD: # gitlab web前端配置

  # sonar配置，参考文档 https://doc.djicorp.com/view/b3324c7b-c39e-4cde-b1e5-e6b6de42dd60#Gitlab%20CI/CD
  # SONAR_LOGIN: # gitlab web前端配置
  # SONAR_URL: # gitlab web前端配置
  # *********************: # gitlab web前端配置

  # 需要在部署阶段中配置，参考文档 https://doc.djicorp.com/view/59d57c49-3998-41d8-9343-f50c73eaa86c#可以定制的选项
  # 部署区域名称
  COMMON_DEPLOY_REGION: "hk"
  DEV_DEPLOY_REGION: "shenzhen_aliyun"
  # 部署环境-dev环境
  DEV_DEPLOY_ENV: "dev"
  # 部署环境-test环境
  TEST_DEPLOY_ENV: "test"
  # 部署环境-stag环境
  STAG_DEPLOY_ENV: "stag"
  #部署环境
  PROD_DEPLOY_ENV: "prod"

  # changelog配置，参考文档 https://doc.djicorp.com/view/dfe4859a-ce9a-402f-be98-ac94d72380d3#自动打版本tag
  # CHANGELOG_TOKEN: # gitlab web前端配置
  # CHANGELOG_USER: # gitlab web前端配置

  # TODO L5用例应该怎么做？
  # 原理：通过这里（gitlab）触发统一平台上的自动化用例执行自动化测试
  # 需要在api测试阶段中配置，参考统一测试平台文档 https://doc.djicorp.com/view/2b3d6684-4650-47b2-ac93-f362ebe79497
  TEST_IDS: "testcase_14173" # 需要执行的用例或场景，多个用英文逗号分割，格式:类型_id，用例为testcase,场景为testsuite,定时任务为schedule, 如：testcase_1,testcase_2,testsuite_1,testcase_3,schedule_1
  TASK_NAME: "L5_test" # 自定义任务名称
  ENV_NAME: "Test"   # 和统一测试平台上配置的环境名称对应（接口模块->环境模块->环境名称,注意不是环境类型）
  EMAIL: "{'recipients':['${GITLAB_USER_NAME}']}"
  wait_for_deploy: "30s"

#################### Static Test ####################

unit-test:
  stage: static-test
  image: $MVN_IMAGE
  script:
    - mvn -T 4C clean test -Dconfserver_seckey_pipeline_mario_be=$confserver_seckey_pipeline_mario_be -Dmaven.test.failure.ignore=true | grep -v 'Downloaded\|Downloading'
  # 注意，这里是为了sonar扫描时，能获取单元测试时，各个子模块的覆盖率，和path上面Variables配置的target对应
  artifacts:
    paths:
      - $APP_TARGET
      - $CORE_TARGET
      - $TARGET
  extends:
    - .unit-test-template
  rules:
    - if: '$CI_COMMIT_BRANCH =~ /(?i)^(feature|hotfix).+/ && $CI_PIPELINE_SOURCE == "push"'
    - if: '$CI_COMMIT_BRANCH =~ /(?i)^(version).+/'
    - if: '$CI_COMMIT_BRANCH == "develop"'
    - if: '$CI_COMMIT_BRANCH == "test"'
    - if: '$CI_COMMIT_BRANCH == "stag"'
    - if: '$CI_COMMIT_BRANCH == "master"'



#################### Sonar Test ####################
# 如需要覆写sonar-test的script命令，请带上-Dsonar.qualitygate.wait=true -Dsonar.analysis.pipeline_id=${CI_PIPELINE_ID} 和 -Dsonar.analysis.ci_server_host=${CI_SERVER_HOST},其中CI_PIPELINE_ID和CI_SERVER_HOST不需要配置，容器里参数，SONAR_URL值=https://sonarqube.djicorp.com

sonar-test:
  stage: sonar-test
  image: $MVN_IMAGE
  script:
    - mvn -B org.codehaus.mojo:sonar-maven-plugin:3.11.0.3922:sonar -Dsonar.qualitygate.wait=true -Dsonar.analysis.pipeline_id=${CI_PIPELINE_ID} -Dsonar.analysis.ci_server_host=${CI_SERVER_HOST} -Dsonar.host.url=$SONAR_URL -Dsonar.projectKey=$SONARQUBE_PROJECT_NAME -Dsonar.login=$SONAR_LOGIN -Dsonar.gitlab.ref_name=$CI_COMMIT_REF_NAME -Dsonar.gitlab.project_id=$CI_PROJECT_ID -Dsonar.branch.name=$CI_COMMIT_REF_NAME -Dpmd.skip=true -Dcheckstyle.skip=true
  extends:
    - .sonar-test-template
  rules:
    - if: '$CI_COMMIT_BRANCH =~ /(?i)^(feature|hotfix).+/ && $CI_PIPELINE_SOURCE == "push"'
    - if: '$CI_COMMIT_BRANCH =~ /(?i)^(version).+/'
    - if: '$CI_COMMIT_BRANCH == "develop"'
    - if: '$CI_COMMIT_BRANCH == "test"'
    - if: '$CI_COMMIT_BRANCH == "stag"'
    - if: '$CI_COMMIT_BRANCH == "master"'

#################### Build ####################
build:
  stage: build
  image: harbor.djicorp.com/library/maven-kaniko:3.8.3-jdk-8-202208161900
  extends:
    - .java-eci-build-template
  rules:
    - if: '$CI_COMMIT_BRANCH =~ /(?i)^(version).+/'
    - if: '$CI_COMMIT_BRANCH == "develop"'
    - if: '$CI_COMMIT_BRANCH == "test"'
    - if: '$CI_COMMIT_BRANCH == "stag"'
    - if: '$CI_COMMIT_BRANCH == "master"'


#################### Deploy ####################
# 注意：下面的dev-deploy为job名称，只能以中横线加前缀
# 具体规范参考 [DAVA项目使用流水线模板说明](https://doc.djicorp.com/view/3d34108e-6f71-43a9-9cb4-bb481f21913b#%E6%A8%A1%E6%9D%BF%E7%BB%93%E6%9E%84%E8%AF%B4%E6%98%8E)

dev-deploy:
  stage: deploy
  variables: # 需要配置stag部署参数
    GIT_STRATEGY: none  # 此构建不需要拉取代码
    DEPLOY_REGION: ${DEV_DEPLOY_REGION} # 需要配置
    DEPLOY_ENV: ${DEV_DEPLOY_ENV} # 需要配置
  extends:
    - .deploy-template
  rules:
    - if: '$CI_COMMIT_BRANCH =~ /(?i)^(version).+/'
    - if: '$CI_COMMIT_BRANCH == "develop"'

test-deploy:
  stage: deploy
  variables:
    GIT_STRATEGY: none  # 此构建不需要拉取代码
    DEPLOY_REGION: "shenzhen" # 需要配置
    DEPLOY_ENV: ${TEST_DEPLOY_ENV} # 需要配置
  extends:
    - .deploy-template
  rules:
    - if: '$CI_COMMIT_BRANCH == "test"'

stag-deploy:
  stage: deploy
  variables:
    GIT_STRATEGY: none  # 此构建不需要拉取代码
    DEPLOY_REGION: ${COMMON_DEPLOY_REGION} # 需要配置
    DEPLOY_ENV: ${STAG_DEPLOY_ENV} # 需要配置
  extends:
    - .deploy-template
  rules:
    - if: '$CI_COMMIT_BRANCH == "stag"'


  # 备注:
  # 上面deploy，如果有配置Jacoco的，可以在CD的时候触发生成对应环境的Jacoco覆盖率报告

before_script:
  # CD触发Jacoco报告
  - echo $CI_COMMIT_REF_SLUG
  - curl -H "Content-Type:application/json" -H "GitLab:GitLab-$GITLAB_USER_NAME" -X POST -d '{"appid":"'"${PAMS_APPLICATION_EN_NAME}"'","envType":"'"${DEV_DEPLOY_ENV}"'","versionDiff":"master","versionNow":"'"${CI_COMMIT_REF_SLUG}"'"}' "https://q.djicorp.com/jacoco/server/api/receiveNewJacocoTask"
  - sleep 10s

#################### Integration Test ####################

api-test:
  stage: integration-test
  variables: #需要配置stag接口测试参数
    GIT_STRATEGY: none #无需拉取代码
  extends:
    - .api-test-template
  rules:
    - if: '$CI_COMMIT_BRANCH == "test"'
    - if: '$CI_COMMIT_BRANCH == "stag"'

#################### Dependency Test ####################

dependency-test:
  stage: static-test
  extends:
    - .java-dependency-test-template
  rules:
    - if: '$CI_COMMIT_BRANCH =~ /(?i)^(feature).+/ && $CI_PIPELINE_SOURCE == "push"'
    - if: '$CI_COMMIT_BRANCH =~ /(?i)^(version).+/'
    - if: '$CI_COMMIT_BRANCH == "test"'
    - if: '$CI_COMMIT_BRANCH == "stag"'


#################### quality gate  ####################

quality-gate:
  extends:
    - .quality-gate-test-template
  rules:
    - if: '$CI_COMMIT_BRANCH =~ /(?i)^(feature).+/ && $CI_PIPELINE_SOURCE == "push"'
    - if: '$CI_COMMIT_BRANCH =~ /(?i)^(version).+/'
    - if: '$CI_COMMIT_BRANCH == "test"'
    - if: '$CI_COMMIT_BRANCH == "stag"'