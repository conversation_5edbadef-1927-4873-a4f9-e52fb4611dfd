package com.dji.message.config;

import io.choerodon.core.oauth.CustomUserDetails;
import io.choerodon.core.oauth.DetailsHelper;
import io.opentracing.SpanContext;
import io.opentracing.contrib.web.servlet.filter.TracingFilter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.InetAddress;
import java.net.URLEncoder;
import java.util.UUID;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new LogInterceptor());
    }
}


class LogInterceptor extends HandlerInterceptorAdapter {
    private final Logger logger = LoggerFactory.getLogger(LogInterceptor.class);

    private static final String UNKNOWN = "unknown";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String clientIp = getRemoteIP(request);
        String uri = request.getServletPath();
        String method = request.getMethod();
        String invokeId = UUID.randomUUID().toString();
        request.setAttribute("invokeId", invokeId);
        MDC.put("invoke_id", invokeId);
        MDC.put("client_ip", clientIp);
        MDC.put("server_ip", getLocalHostIP());
        MDC.put("server_host", getLocalHostName());
        MDC.put("uri", uri);
        MDC.put("method", method);
        MDC.put("user", getUser());
        MDC.put("referer", request.getHeader("referer"));
        MDC.put("user_agent", request.getHeader("User-Agent"));
        // 请求 id，通过该 id 可以查询得到本次请求所有的日志信息，一般是API约定的请求方传进来
        MDC.put("request_id", StringUtils.isNotBlank(request.getSession().getId()) ? request.getSession().getId() : UUID.randomUUID().toString());
        MDC.put("log_uuid", UUID.randomUUID().toString());
        MDC.put("trace_id", getTracing());
        // 网关请求ID在每次请求发生时获取，需要sdk配合在发起请求时设置这个参数或者返回这个参数，否则取不到该参数
        MDC.put("request_at", System.currentTimeMillis() + "");
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        MDC.put("status", response.getStatus() + "");
        String requestAt = MDC.get("request_at");
        long duration = 0L;
        if (StringUtils.isNotBlank(requestAt)) {
            duration = System.currentTimeMillis() - NumberUtils.toLong(requestAt, 0);
        }
        MDC.put("duration",duration + "");
        super.postHandle(request, response, handler, modelAndView);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        super.afterCompletion(request, response, handler, ex);
    }

    /**
     * 获取用户真实IP地址，不使用request.getRemoteAddr();的原因是有可能用户使用了代理软件方式避免真实IP地址,
     * 可是，如果通过了多级反向代理的话，X-Forwarded-For的值并不止一个，而是一串IP值，究竟哪个才是真正的用户端的真实IP呢？
     * 答案是取X-Forwarded-For中第一个非unknown的有效IP字符串。
     * 如：X-Forwarded-For：*************, *************, *************,
     * *************
     * 用户真实IP为： *************
     *
     * @param request
     * @return
     */
    private String getRemoteIP(HttpServletRequest request) {
        String ip = "";
        try {
            ip = request.getHeader("x-forwarded-for");
            if (ip == null || ip.length() == 0 || ip.equalsIgnoreCase(UNKNOWN)) {
                ip = request.getHeader("Proxy-Client-IP");
            }
            if (ip == null || ip.length() == 0 || ip.equalsIgnoreCase(UNKNOWN)) {
                ip = request.getHeader("WL-Proxy-Client-IP");
            }
            if (ip == null || ip.length() == 0 || ip.equalsIgnoreCase(UNKNOWN)) {
                ip = request.getRemoteAddr();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }

        return ip;
    }

    /**
     * 获取trace id
     * @return
     */
    private String getTracing() {
        String traceId = "";
        try {
            ServletRequestAttributes attr = null;
            try {
                attr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
            } catch (Exception e) {
                logger.warn("this is not http request,can't get httprequest");
            }

            if (attr != null) {
                HttpServletRequest request = attr.getRequest();
                if (request != null) {
                    SpanContext spanContext = (SpanContext) request.getAttribute(TracingFilter.SERVER_SPAN_CONTEXT);
                    if (spanContext != null) {
                        traceId = encodedValue(spanContext.toString());
                    }
                }
            }
        } catch (Exception e) {
            logger.error("getTraceId error", e);
        }
        logger.info("tracing info:{}", traceId);
        return traceId;
    }


    private String encodedValue(String value) {
        try {
            return URLEncoder.encode(value, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            // not much we can do, try raw value
            return value;
        }
    }

    /**
     * 获取本机的IP
     *
     * @return Ip地址
     */
    private String getLocalHostIP() {
        String ip;
        try {
            /** 返回本地主机。 */
            InetAddress addr = InetAddress.getLocalHost();
            /** 返回 IP 地址字符串（以文本表现形式） */
            ip = addr.getHostAddress();
        } catch (Exception ex) {
            ip = "";
        }

        return ip;
    }

    /**
     * 获取主机名：
     *
     * @return
     */
    private String getLocalHostName() {
        String hostName;
        try {
            /** 返回本地主机。 */
            InetAddress addr = InetAddress.getLocalHost();
            /** 获取此 IP 地址的主机名。 */
            hostName = addr.getHostName();
        } catch (Exception ex) {
            hostName = "";
        }

        return hostName;
    }

    /**
     * 获取用户
     * @return
     */
    private String getUser() {
        String user;
        try {
            CustomUserDetails userDetail = DetailsHelper.getUserDetails();
            if (userDetail != null) {
                user = String.valueOf(userDetail.getUserId());
            } else {
                user = "";
            }
        } catch (Exception ex) {
            user = "";
        }
        return user;
    }

}
