package org.hzero.message.infra.constant;

/**
 * <AUTHOR>
 */
public class ErrorCodeConstant {

    public static final String ERROR_ATTACH_NOT_EXIST = "dji.msg.error.second.content.not.exist";

    /**
     * 数据错误
     **/
    public static final String ERROR_DATA_VALID = "dji.msg.error.data.valid";

    /**
     * 场景id格式错误,请检查后重新确认
     **/
    public static final String ERROR_WRONG_DATA_FORMAT = "dji.msg.error.wrong.data.format";

    /**
     * 当前填写的消息模板无效,请检查后重新确认
     **/
    public static final String ERROR_INVALID_MESSAGE_TEMPLATE = "dji.msg.error.invalid.message.template";

    /**
     * 当前填写的发件邮箱无效,请检查后重新确认
     **/
    public static final String ERROR_INVALID_SEND_EMAIL = "dji.msg.error.invalid.send.email";

    /**
     * 当前场景+组别+语种下已维护规则,请检查后重新确认
     **/
    public static final String ERROR_UNIQUE_RULE_REPEATS = "dji.msg.error.unique.rule.repeats";

    /**
     * 发件落款和消息模板中不得包含非当前品牌的关键词信息,请检查后重新确认
     **/
    public static final String ERROR_THE_CONTENT_DOES_NOT_MATCH_THE_BRAND = "dji.msg.error.the.content.does.not.match.the.brand";

    /**
     * 导入数据重复,请检查数据
     **/
    public static final String IMPORT_DATA_REPEAT = "dji.o2om_c.error.import.data.repeat";

    /**
     * 导入数据超出上线,请检查数据!
     **/
    public static final String ERROR_IMPORT_DATA_EXCEEDS_UPPER_LIMIT = "dji.o2om_c.error.import.data.exceeds.upper.limit";

    /**
     * 组别不存在,请检查数据
     **/
    public static final String ERROR_REQUIREMENT_PLAN_BUSINESS_GROUP_NOT_FOUND = "dji.o2om_c.error.requirement.plan.business.group.not.found";

    /**
     * 无邮件发送规则配置,请检查数据
     **/
    public static final String ERROR_NO_EMAIL_SEND_RULES_CONFIG = "dji.msg.error.no.email.send.rules.config";

    /**
     * 邮件发送规则配置重复
     **/
    public static final String ERROR_SIZE_EMAIL_SEND_RULES_CONFIG = "dji.msg.error.size.email.send.rules.config";

}
