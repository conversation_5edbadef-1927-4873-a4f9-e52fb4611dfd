package org.hzero.boot.imported.app.service;


import org.hzero.boot.imported.domain.entity.*;

import java.util.*;

/**
 * 全量校验 Handler
 *
 * <AUTHOR> 2021/9/3 15:33
 **/
public abstract class AbstractFullValidatorHandler {
    /**
     * 自定义校验
     *
     * @param dataList 数据
     * @return 是否校验成功
     */
    public abstract boolean validate(List<ImportData> dataList);

    /**
     * 数据对象
     */
    private final ThreadLocal<List<ImportData>> contextList = new ThreadLocal();
    /**
     * 自定义参数
     */
    private final ThreadLocal<Map<String, Object>> args = new ThreadLocal();

    public AbstractFullValidatorHandler() {}

    public List<ImportData> getContextList() {
        return (List) this.contextList.get();
    }

    public AbstractFullValidatorHandler setContextList(List<ImportData> contextList) {
        this.contextList.set(contextList);
        return this;
    }

    public Map<String, Object> getArgs() {
        return (Map) this.args.get();
    }

    public AbstractFullValidatorHandler setArgs(Map<String, Object> args) {
        this.args.set(args);
        return this;
    }

    public <T> T getArgs(String key) {
        return (T) ((Map) this.args.get()).get(key);
    }

    public void putArgs(String key, Object value) {
        Map<String, Object> map = (Map) this.args.get();
        map.put(key, value);
        this.args.set(map);
    }

    public void clear() {
        this.contextList.remove();
        this.args.remove();
    }
}
