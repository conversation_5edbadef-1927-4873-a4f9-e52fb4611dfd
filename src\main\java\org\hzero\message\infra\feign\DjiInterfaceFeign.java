package org.hzero.message.infra.feign;


import org.hzero.message.api.dto.*;
import org.hzero.message.infra.feign.impl.*;
import org.springframework.cloud.openfeign.*;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;


/**
 * interface feign
 *
 * <AUTHOR> 2020/12/29 17:12
 **/
@FeignClient(value = "${hzero.service.interface.name:hzero-interface}", fallback = DjiInterfaceFeignFallback.class)
public interface DjiInterfaceFeign {

    /**
     * 创建接口日志
     *
     * @param organizationId  租户id
     * @param interfaceLogDTO 接口日志DTO
     */
    @PostMapping("/v1/{organizationId}/interface-logs")
    ResponseEntity<Integer> createLog(@PathVariable("organizationId") Long organizationId, @RequestBody DjiInterfaceLogDTO interfaceLogDTO);

}
