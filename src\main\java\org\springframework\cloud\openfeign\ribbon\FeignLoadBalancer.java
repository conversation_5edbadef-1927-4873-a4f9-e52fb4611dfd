package org.springframework.cloud.openfeign.ribbon;

import com.netflix.client.*;
import com.netflix.client.config.*;
import com.netflix.loadbalancer.*;
import feign.*;
import org.springframework.cloud.netflix.ribbon.*;
import org.springframework.http.*;

import java.io.*;
import java.net.*;
import java.util.*;

import static org.springframework.cloud.netflix.ribbon.RibbonUtils.*;

/**
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 * <AUTHOR>
 */
public class FeignLoadBalancer extends
        AbstractLoadBalancerAwareClient<FeignLoadBalancer.RibbonRequest, FeignLoadBalancer.RibbonResponse> {

    private static final String DJI_FILE_FEIGN_FILE2ID_URL = "/v3/0/attach/file/upload";

    private final RibbonProperties ribbon;
    protected int connectTimeout;
    protected int readTimeout;
    protected IClientConfig clientConfig;
    protected ServerIntrospector serverIntrospector;

    public FeignLoadBalancer(ILoadBalancer lb, IClientConfig clientConfig,
                             ServerIntrospector serverIntrospector) {
        super(lb, clientConfig);
        this.setRetryHandler(RetryHandler.DEFAULT);
        this.clientConfig = clientConfig;
        this.ribbon = RibbonProperties.from(clientConfig);
        RibbonProperties ribbon = this.ribbon;
        this.connectTimeout = ribbon.getConnectTimeout();
        this.readTimeout = ribbon.getReadTimeout();
        this.serverIntrospector = serverIntrospector;
    }

    @Override
    public RibbonResponse execute(RibbonRequest request, IClientConfig configOverride)
            throws IOException {
        Request.Options options;
        if(DJI_FILE_FEIGN_FILE2ID_URL.equals(request.getUri().getPath())) {
            // 客制化文件上传超时时间
            options = new Request.Options(600000, 600000);
        } else {
            if (configOverride != null) {
                RibbonProperties override = RibbonProperties.from(configOverride);
                options = new Request.Options(
                        override.connectTimeout(this.connectTimeout),
                        override.readTimeout(this.readTimeout));
            }
            else {
                options = new Request.Options(this.connectTimeout, this.readTimeout);
            }
        }
        Response response = request.client().execute(request.toRequest(), options);
        return new RibbonResponse(request.getUri(), response);
    }

    @Override
    public RequestSpecificRetryHandler getRequestSpecificRetryHandler(
            RibbonRequest request, IClientConfig requestConfig) {
        if (this.ribbon.isOkToRetryOnAllOperations()) {
            return new RequestSpecificRetryHandler(true, true, this.getRetryHandler(),
                    requestConfig);
        }
        if (!request.toRequest().method().equals("GET")) {
            return new RequestSpecificRetryHandler(true, false, this.getRetryHandler(),
                    requestConfig);
        }
        else {
            return new RequestSpecificRetryHandler(true, true, this.getRetryHandler(),
                    requestConfig);
        }
    }

    @Override
    public URI reconstructURIWithServer(Server server, URI original) {
        URI uri = updateToSecureConnectionIfNeeded(original, this.clientConfig, this.serverIntrospector, server);
        return super.reconstructURIWithServer(server, uri);
    }

    protected static class RibbonRequest extends ClientRequest implements Cloneable {

        private final Request request;
        private final Client client;

        RibbonRequest(Client client, Request request, URI uri) {
            this.client = client;
            setUri(uri);
            this.request = toRequest(request);
        }

        private Request toRequest(Request request) {
            Map<String, Collection<String>> headers = new LinkedHashMap<>(
                    request.headers());
            return Request.create(request.method(),getUri().toASCIIString(),headers,request.body(),request.charset());
        }

        Request toRequest() {
            return toRequest(this.request);
        }

        Client client() {
            return this.client;
        }

        HttpRequest toHttpRequest() {
            return new HttpRequest() {
                @Override
                public HttpMethod getMethod() {
                    return HttpMethod.resolve(RibbonRequest.this.toRequest().method());
                }

                @Override
                public String getMethodValue() {
                    return getMethod().name();
                }

                @Override
                public URI getURI() {
                    return RibbonRequest.this.getUri();
                }

                @Override
                public HttpHeaders getHeaders() {
                    Map<String, List<String>> headers = new HashMap<>();
                    Map<String, Collection<String>> feignHeaders = RibbonRequest.this.toRequest().headers();
                    for(String key : feignHeaders.keySet()) {
                        headers.put(key, new ArrayList<String>(feignHeaders.get(key)));
                    }
                    HttpHeaders httpHeaders = new HttpHeaders();
                    httpHeaders.putAll(headers);
                    return httpHeaders;

                }
            };
        }


        @Override
        public Object clone() {
            return new RibbonRequest(this.client, this.request, getUri());
        }
    }

    protected static class RibbonResponse implements IResponse {

        private final URI uri;
        private final Response response;

        RibbonResponse(URI uri, Response response) {
            this.uri = uri;
            this.response = response;
        }

        @Override
        public Object getPayload() throws ClientException {
            return this.response.body();
        }

        @Override
        public boolean hasPayload() {
            return this.response.body() != null;
        }

        @Override
        public boolean isSuccess() {
            return this.response.status() == 200;
        }

        @Override
        public URI getRequestedURI() {
            return this.uri;
        }

        @Override
        public Map<String, Collection<String>> getHeaders() {
            return this.response.headers();
        }

        Response toResponse() {
            return this.response;
        }

        @Override
        public void close() throws IOException {
            if (this.response != null && this.response.body() != null) {
                this.response.body().close();
            }
        }

    }

}

