package org.hzero.message.api.controller.v1;

import io.choerodon.core.iam.ResourceLevel;
import io.choerodon.swagger.annotation.Permission;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.hzero.boot.message.entity.*;
import org.hzero.message.app.service.DjiSendEmailService;
import org.hzero.message.app.service.EmailSendService;
import org.hzero.message.config.DjiMessageSwaggerApiConfig;
import org.hzero.message.domain.entity.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * description 消息发送
 *
 * <AUTHOR> 2020/11/18 21:15
 */
@Api(tags = DjiMessageSwaggerApiConfig.SEND_MESSAGE)
@RestController("djiSendMessageController.v1")
@RequestMapping("/v1/dji-send-message")
public class DjiSendMessageController {

    @Autowired
    private EmailSendService emailSendService;

    @Autowired
    private DjiSendEmailService djiSendEmailService;

    @ApiOperation(value = "邮件发送")
    @Permission(level = ResourceLevel.ORGANIZATION)
    @PostMapping("/send-email")
    public Message sendEmail(@RequestBody MessageSender messageSender) {
        return emailSendService.sendMessage(messageSender);
    }


    @ApiOperation(value = "邮件批量发送")
    @Permission(level = ResourceLevel.ORGANIZATION)
    @PostMapping("/batch-send-email")
    public Message batchSendEmail(@RequestBody List<MessageSender> messageSender) {
        return djiSendEmailService.sendMessage(messageSender);
    }

    @ApiOperation(value = "邮件批量发送无权限")
    @PostMapping("/batchSendEmailNoAuth")
    @Permission(permissionPublic = true)
    public Message batchSendEmailNoAuth(@RequestBody List<MessageSender> messageSender) {
        return djiSendEmailService.sendMessage(messageSender);
    }

    @ApiOperation(value = "获取消息模板")
    @Permission(level = ResourceLevel.ORGANIZATION)
    @GetMapping("/get-messageTemplate")
    public MessageTemplate getMessageTemplate(@RequestParam @ApiParam(value = "租户ID", required = true) Long tenantId,
                                              @RequestParam @ApiParam(value = "模板编码", required = true) String templateCode) {
        return djiSendEmailService.MessageTemplate(tenantId,templateCode);
    }
}
