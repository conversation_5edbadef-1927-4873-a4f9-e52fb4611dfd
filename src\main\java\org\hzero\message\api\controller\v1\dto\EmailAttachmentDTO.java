package org.hzero.message.api.controller.v1.dto;

import lombok.Data;

/**
 * 附件DTO
 *
 * <AUTHOR>
 */
@Data
public class EmailAttachmentDTO {

    /**
     *
     *附件内容，Base64编码
     */
    private String content;

    /**
     *
     *附件文件名
     */
    private String name;

    /**
     *
     *需要注意，当inline为true且定义了contentId时，邮件正文可以通过cid的方式引用附件，不需要这个功能的话可以不用提供
     */
    private String inline;

    /**
     *
     *需要注意，当inline为true且定义了contentId时，邮件正文可以通过cid的方式引用附件，不需要这个功能的话可以不用提供
     */
    private String contentId;


}
