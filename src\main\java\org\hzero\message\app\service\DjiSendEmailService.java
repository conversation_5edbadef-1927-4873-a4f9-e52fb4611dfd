package org.hzero.message.app.service;

import org.hzero.boot.message.entity.MessageSender;
import org.hzero.boot.message.entity.MessageTemplate;
import org.hzero.message.domain.entity.Message;

import java.util.List;

/**
 * description 邮件发送
 *
 * <AUTHOR> 2021/03/19 16:12
 */
public interface DjiSendEmailService {

	/**
	 *
	 * 批量发送邮件
	 * <AUTHOR>
	 * @param messageSender 入参
	 * @return org.hzero.message.domain.entity.Message
	 */
	Message sendMessage(List<MessageSender> messageSender);

	MessageTemplate MessageTemplate(Long tenantId , String templateCode);

}
