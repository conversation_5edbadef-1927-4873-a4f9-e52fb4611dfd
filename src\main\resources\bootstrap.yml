server:
  port: 8120
management:
  server:
    port: 8121
  endpoints:
    web:
      exposure:
        include: '*'

spring:
  profiles:
    active: ${SPRING_PROFILES_ACTIVE:default}
  cloud:
    config:
      fail-fast: false
      # 是否启用配置中心
      enabled: ${SPRING_CLOUD_CONFIG_ENABLED:false}
      # 配置中心地址
      uri: ${SPRING_CLOUD_CONFIG_URI:http://dev.hzero.org:8010}
      retry:
        # 最大重试次数
        maxAttempts: 6
        multiplier: 1.1
        # 重试间隔时间
        maxInterval: 2000
      # 标签
      label: ${SPRING_CLOUD_CONFIG_LABEL:}

eureka:
  instance:
    # 以IP注册到注册中心
    preferIpAddress: ${EUREKA_INSTANCE_PREFER_IP_ADDRESS:true}
    leaseRenewalIntervalInSeconds: 10
    leaseExpirationDurationInSeconds: 30
    # 服务的一些元数据信息
    metadata-map:
      VERSION: 1.4.7.RELEASE
  client:
    serviceUrl:
      # 注册中心地址
      defaultZone: ${EUREKA_DEFAULT_ZONE:http://dev.hzero.org:8000/eureka}
    registryFetchIntervalSeconds: 10
    disable-delta: true

hystrix:
  threadpool:
    default:
      # 执行命令线程池的核心线程数，也是命令执行的最大并发量
      # 默认10
      coreSize: 1000
      # 最大执行线程数
      maximumSize: 1000
  command:
    default:
      execution:
        isolation:
          thread:
            # HystrixCommand 执行的超时时间，超时后进入降级处理逻辑。一个接口，理论的最佳响应速度应该在200ms以内，或者慢点的接口就几百毫秒。
            # 默认 1000 毫秒，最高设置 2000足矣。如果超时，首先看能不能优化接口相关业务、SQL查询等，不要盲目加大超时时间，否则会导致线程堆积过多，hystrix 线程池卡死，最终服务不可用。
            timeoutInMilliseconds: ${HYSTRIX_COMMAND_TIMEOUT_IN_MILLISECONDS:90000}
    # 文件异步导出配置
    # 覆盖了 org.hzero.boot.platform.export.DefaultExportAsyncTemplate 重写了上传文件逻辑
    # 覆盖了 org.hzero.export.ExportColumnHelper 解决了导出名称重复问题
    # 覆盖了 org.springframework.cloud.openfeign.ribbon.FeignLoadBalancer 重写了指定feign url的 ribbon 超时时间
    # 注意：如果文件过大
    # 出现 HystrixTimeoutException 请调大FILE2ID_COMMAND_TIMEOUT_IN_MILLISECONDS时间
    # 出现 feign.RetryableException 请调大FeignLoadBalancer类的时间
    DjiMarketingFeign#transformCmPdf(String):
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: ${FILE2ID_COMMAND_TIMEOUT_IN_MILLISECONDS:900000}