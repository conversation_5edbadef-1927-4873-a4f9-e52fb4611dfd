//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package org.hzero.message.app.service;

import io.choerodon.core.domain.Page;
import io.choerodon.mybatis.pagehelper.domain.PageRequest;
import java.util.List;
import org.hzero.message.domain.entity.NoticePublished;

public interface NoticePublishedService {
    NoticePublished publicNotice(List<Long> publishedIds, Long noticeId, Long organizationId);

    Page<NoticePublished> listNoticePublished(Long tenantId, PageRequest pageRequest, Long noticeId);
}
