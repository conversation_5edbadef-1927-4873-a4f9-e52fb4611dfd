package org.hzero.message.app.service.impl;

import com.alibaba.fastjson.JSON;
import com.dji.dava.gateway.httpclient.GatewayClient;
import com.dji.dava.gateway.httpclient.Response;
import io.choerodon.core.convertor.ApplicationContextHelper;
import io.choerodon.core.exception.CommonException;
import lombok.extern.slf4j.*;
import org.hzero.boot.message.entity.Attachment;
import org.hzero.boot.message.entity.MessageSender;
import org.hzero.boot.message.entity.Receiver;
import org.hzero.core.base.BaseConstants;
import org.hzero.core.message.MessageAccessor;
import org.hzero.message.api.controller.v1.dto.EmailAttachmentDTO;
import org.hzero.message.api.dto.UserMessageDTO;
import org.hzero.message.app.service.EmailSendService;
import org.hzero.message.app.service.EmailServerService;
import org.hzero.message.app.service.MessageGeneratorService;
import org.hzero.message.app.service.MessageReceiverService;
import org.hzero.message.config.MessageConfig;
import org.hzero.message.config.MessageConfigProperties;
import org.hzero.message.constant.SpfmConstants;
import org.hzero.message.domain.entity.*;
import org.hzero.message.domain.repository.EmailFilterRepository;
import org.hzero.message.domain.repository.MessageReceiverRepository;
import org.hzero.message.domain.repository.MessageRepository;
import org.hzero.message.domain.repository.MessageTransactionRepository;
import org.hzero.message.domain.service.IMessageLangService;
import org.hzero.message.domain.vo.EmailRequestVO;
import org.hzero.message.infra.constant.HmsgConstant;
import org.hzero.message.infra.exception.SendMessageException;
import org.hzero.message.infra.mapper.DjiUserGroupMapper;
import org.hzero.message.infra.supporter.EmailSupporter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: c-halk.liu
 * @Description:
 * @Date: 2020/10/14 15:28
 */
@Slf4j
@Service
public class EmailSendServiceImpl extends AbstractSendService implements EmailSendService {

    private static final Logger logger = LoggerFactory.getLogger(EmailSendServiceImpl.class);

    private static final int SUCCESS_CODE = 200;
    private static final String RESULT_CODE="resultCode";
    private static final String UMS_DEFAULT_SERVER_CODE = "HZERO";

    @Autowired
    private MessageConfig messageConfig;

    @Autowired
    private EmailFilterRepository emailFilterRepository;

    @Autowired
    private MessageReceiverRepository messageReceiverRepository;

    @Autowired
    private EmailServerService emailServerService;

    @Autowired
    private MessageGeneratorService messageGeneratorService;

    @Autowired
    private MessageReceiverService messageReceiverService;

    @Autowired
    private MessageTransactionRepository messageTransactionRepository;

    @Autowired
    private MessageRepository messageRepository;

    @Autowired
    private DjiUserGroupMapper djiUserGroupMapper;

    @Autowired
    private MessageConfigProperties messageConfigProperties;
    @Autowired
    private IMessageLangService messageLangService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Message sendMessage(MessageSender messageSender) {
        //判断用户组编码是否为空，不为空走用户组
        //为空走默认传的发件人
        if (StringUtils.hasText(messageSender.getGroupCode())) {
            List<Receiver> list = djiUserGroupMapper.selectUserGroupList(messageSender.getGroupCode());
            messageSender.setReceiverAddressList(list);
        }
        return sendMessage(messageSender, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Message sendMessage(MessageSender messageSender, Integer tryTimes) {
        Message result = null;
        if (messageConfigProperties.isAsync()) {
            ApplicationContextHelper.getContext().getBean(EmailSendService.class).asyncSendMessage(messageSender, tryTimes);
        } else {
            // 兼容甄云的多语言处理方式，按照语言分组
            List<MessageSender> senderList = messageLangService.getLang(messageSender);
            for (MessageSender sender : senderList) {
                logger.info("走到这里sender:{}", JSON.toJSONString(sender));
                result = sendMessageWithLang(sender, tryTimes);
            }
        }
        return result;
    }

    @Override
    @Async("commonAsyncTaskExecutor")
    public void asyncSendMessage(MessageSender messageSender, Integer tryTimes) {
        // 兼容甄云的多语言处理方式，按照语言分组
        List<MessageSender> senderList = messageLangService.getLang(messageSender);
        for (MessageSender sender : senderList) {
            sendMessageWithLang(sender, tryTimes);
        }
    }

    private Message sendMessageWithLang(MessageSender messageSender, Integer tryTimes) {
        // 生成消息记录
        Message message = createMessage(messageSender, HmsgConstant.MessageType.EMAIL);
        try {
            // 获取消息内容
            message = messageGeneratorService.generateMessage(messageSender, message);
            // 邮件附件
            message.setAttachmentList(messageSender.getAttachmentList());
            // 抄送
            message.setCcList(messageSender.getCcList());
            // 密送
            message.setBccList(messageSender.getBccList());
            // 获取消息接收人
            messageSender = messageReceiverService.queryReceiver(messageSender);
            if (org.springframework.util.CollectionUtils.isEmpty(messageSender.getReceiverAddressList())) {
                messageRepository.updateOptional(message.setSendFlag(BaseConstants.Flag.NO), Message.FIELD_SEND_FLAG);
                MessageTransaction transaction = new MessageTransaction()
                        .setMessageId(message.getMessageId())
                        .setTrxStatusCode(HmsgConstant.TransactionStatus.P)
                        .setTenantId(message.getTenantId())
                        .setTransactionMessage(MessageAccessor.getMessage(HmsgConstant.ErrorCode.NO_RECEIVER).desc());
                messageTransactionRepository.insertSelective(transaction);
                message.setTransactionId(transaction.getTransactionId());
                return message;
            }
            // 获取邮箱配置
            EmailServer emailServer = emailServerService.getEmailServer(messageSender.getTenantId(), messageSender.getServerCode());
            validServer(emailServer, messageSender.getTenantId(), messageSender.getServerCode());
            // 覆盖默认的邮件重试次数
            if (tryTimes != null) {
                emailServer.setTryTimes(tryTimes);
            }
            // 发送消息
            messageRepository.updateByPrimaryKeySelective(message);
            log.info("Email message: {}", JSON.toJSONString(message));
            log.info("Email messageSender: {}", JSON.toJSONString(messageSender));
            log.info("Email messageSender.getReceiverAddressList(): {}", JSON.toJSONString(messageSender.getReceiverAddressList()));
            log.info("Email messageSender.getBatchSend(): {}", JSON.toJSONString(messageSender.getBatchSend()));
            log.info("Email emailServer: {}", JSON.toJSONString(emailServer));
            sendMessage(messageSender.getReceiverAddressList(), message, emailServer, messageSender.getBatchSend(), messageSender);
            messageRepository.updateByPrimaryKeySelective(message.setSendFlag(BaseConstants.Flag.YES));
            MessageTransaction transaction = new MessageTransaction()
                    .setMessageId(message.getMessageId())
                    .setTrxStatusCode(HmsgConstant.TransactionStatus.S)
                    .setTenantId(message.getTenantId());
            messageTransactionRepository.insertSelective(transaction);
            message.setTransactionId(transaction.getTransactionId());
        } catch (Exception e) {
            logger.error("Send email failed [{} -> {}] : {}", message.getServerCode(), messageSender.getReceiverAddressList(), e.toString());
            failedProcess(message, e);
        }
        return message;
    }

    private void sendMessage(List<Receiver> receiverAddressList, Message message, EmailServer emailServer, Integer batchSend, MessageSender messageSender) {
        JavaMailSender javaMailSender = EmailSupporter.javaMailSender(emailServer);
        int sendCnt = (emailServer.getTryTimes() == null ? 0 : emailServer.getTryTimes()) + 1;
        List<String> emailList = receiverAddressList.stream()
                .map(Receiver::getEmail)
                .filter(StringUtils::hasText)
                .distinct()
                .collect(Collectors.toList());
        // 判断黑白名单
        List<EmailFilter> emailFilterList = emailFilterRepository.select(new EmailFilter().setServerId(emailServer.getServerId()));
        String filterStrategy = emailServer.getFilterStrategy();
        List<String> usableList = new ArrayList<>();
        emailList.forEach(email -> {
                    boolean flag = filterEmail(filterStrategy, emailFilterList, email);
                    if (flag) {
                        messageReceiverRepository.insertSelective(new MessageReceiver()
                                .setMessageId(message.getMessageId())
                                .setTenantId(message.getTenantId())
                                .setReceiverAddress(email)
                                .setFilterFlag(BaseConstants.Flag.NO));
                        usableList.add(email);
                    } else {
                        messageReceiverRepository.insertSelective(new MessageReceiver()
                                .setMessageId(message.getMessageId())
                                .setTenantId(message.getTenantId())
                                .setReceiverAddress(email)
                                .setFilterFlag(BaseConstants.Flag.YES));
                    }
                }
        );
        while (sendCnt > 0) {
            sendCnt--;
            try {
                if (org.springframework.util.CollectionUtils.isEmpty(usableList)) {
                    throw new CommonException(MessageAccessor.getMessage(HmsgConstant.ErrorCode.NULL_EMAIL_LIST).desc());
                }
                log.info("Email send email usableList: {}", JSON.toJSONString(usableList));
                log.info("Email send email message: {}", JSON.toJSONString(message));
                log.info("Email send email emailServer: {}", JSON.toJSONString(emailServer));
                log.info("Email send email batchSend: {}", JSON.toJSONString(batchSend));
                log.info("Email send email messageSender: {}", JSON.toJSONString(messageSender));
                sendEmail(usableList, message, emailServer, javaMailSender, batchSend, messageSender);
                break;
            } catch (Exception e) {
                logger.error("Error send email2", e);
                logger.error("Error send email {}", message);
                if (sendCnt == 0) {
                    throw new CommonException(e);
                }
            }
        }
    }

    /**
     * 黑白名单筛选
     *
     * @param filterStrategy  筛选策略
     * @param emailFilterList 名单
     * @param email           当前邮箱
     */
    private boolean filterEmail(String filterStrategy, List<EmailFilter> emailFilterList, String email) {
        if (filterStrategy == null) {
            filterStrategy = "";
        }
        switch (filterStrategy) {
            case HmsgConstant.FilterStrategy.BLACK:
                for (EmailFilter item : emailFilterList) {
                    String address = item.getAddress();
                    if (address.contains(BaseConstants.Symbol.AT)) {
                        if (Objects.equals(email, address)) {
                            return false;
                        }
                    } else {
                        if (email.endsWith(address)) {
                            return false;
                        }
                    }
                }
                return true;
            case HmsgConstant.FilterStrategy.WHITE:
                for (EmailFilter item : emailFilterList) {
                    String address = item.getAddress();
                    if (address.contains(BaseConstants.Symbol.AT)) {
                        if (Objects.equals(email, address)) {
                            return true;
                        }
                    } else {
                        if (email.endsWith(address)) {
                            return true;
                        }
                    }
                }
                return false;
            default:
                return true;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Message resendMessage(UserMessageDTO message) {
        if (org.springframework.util.CollectionUtils.isEmpty(message.getMessageReceiverList())) {
            return message;
        }
        // 发送消息
        try {
            EmailServer emailServer = emailServerService.getEmailServer(message.getTenantId(), message.getServerCode());
            validServer(emailServer, message.getTenantId(), message.getServerCode());
            sendEmail(message.getMessageReceiverList().stream()
                            .map(MessageReceiver::getReceiverAddress).collect(Collectors.toList()),
                    message, emailServer, EmailSupporter.javaMailSender(emailServer), BaseConstants.Flag.YES, new MessageSender());
            successProcessUpdate(message);
            this.messageRepository.updateOptional(message.setSendFlag(BaseConstants.Flag.YES), new String[]{"sendFlag"});
        } catch (Exception e) {
            logger.error("Send email failed [{} -> {}] : {}", message.getServerCode(), message.getMessageReceiverList(), e.toString());
            failedProcessUpdate(message, e);
        }
        return message;
    }

    private void validServer(EmailServer emailServer, long tenantId, String serverCode) {
        if (emailServer == null || BaseConstants.Flag.NO.equals(emailServer.getEnabledFlag())) {
            throw new SendMessageException(String.format("Email server not found or not enabled : tenantId = [%d] , serverCode = [%s]", tenantId, serverCode));
        }
    }

    /**
     * 邮件发送，邮箱服务器可能使用  DJI 的 UMS或者 阿里云或者  office365
     * @param receiverAddressList   收件人地址
     * @param message   消息
     * @param messageSender 发件人地址
     * @param emailServer   邮箱服务
     * @param javaMailSender    javaMailSender
     * @throws Exception    异常处理
     */
    private void sendEmail(List<String> receiverAddressList, Message message, EmailServer emailServer, JavaMailSender javaMailSender, Integer batchSend, MessageSender messageSender) throws Exception {
        // 添加调试日志，记录传递给EmailSupporter的附件信息
        if (CollectionUtils.isNotEmpty(message.getAttachmentList())) {
            logger.info("EmailSupporter发送邮件 - 附件数量: {}", message.getAttachmentList().size());
            for (int i = 0; i < message.getAttachmentList().size(); i++) {
                Attachment attachment = message.getAttachmentList().get(i);
                logger.info("EmailSupporter发送邮件 - 附件[{}]: fileName={}, fileSize={}",
                    i, attachment.getFileName(), attachment.getFile() != null ? attachment.getFile().length : 0);

                // 处理附件文件名，支持中文等非ASCII字符（使用阿里云推荐方案）
                String fileName = attachment.getFileName();
                if (StringUtils.isEmpty(fileName)) {
                    logger.warn("EmailSupporter发送邮件 - 附件[{}]文件名为空，设置默认文件名", i);
                    fileName = "attachment_" + System.currentTimeMillis() + ".pdf";
                    attachment.setFileName(fileName);
                } else {
                    // 检查文件名是否包含非ASCII字符（如中文）
                    boolean hasNonAscii = !fileName.matches("^[\\x00-\\x7F]*$");
                    if (hasNonAscii) {
                        logger.info("EmailSupporter发送邮件 - 附件[{}]文件名包含非ASCII字符: {}", i, fileName);
                        try {
                            // 使用阿里云官方推荐的MimeUtility.encodeWord方法处理中文附件名
                            String encodedFileName = javax.mail.internet.MimeUtility.encodeWord(fileName, "UTF-8", "B");
                            logger.info("EmailSupporter发送邮件 - 附件[{}]MimeUtility.encodeWord编码后文件名: {}", i, encodedFileName);
                            attachment.setFileName(encodedFileName);
                        } catch (Exception e) {
                            logger.warn("EmailSupporter发送邮件 - 附件[{}]MimeUtility.encodeWord编码失败，使用降级方案: {}", i, e.getMessage());
                            // 降级方案：生成安全的ASCII文件名
                            String safeFileName = generateSafeAsciiFileName(fileName);
                            logger.info("EmailSupporter发送邮件 - 附件[{}]降级后文件名: {}", i, safeFileName);
                            attachment.setFileName(safeFileName);
                        }
                    }
                }
            }
        } else {
            logger.info("EmailSupporter发送邮件 - 无附件");
        }

        if(!UMS_DEFAULT_SERVER_CODE.equals(message.getServerCode())){
            // 开发测试环境不会真的发送邮件，所以开发和测试配置一个默认的测试邮箱
            if (messageConfig.getTestMailbox()) {
                if(StringUtils.isEmpty(messageConfig.getTestMail())){
                    throw new CommonException("current is test model, but the test email is null.");
                }
                message.setCcList(null);
                message.setBccList(null);
                EmailSupporter.sendEmail(javaMailSender, emailServer, message, Arrays.asList(messageConfig.getTestMail().split(BaseConstants.Symbol.COMMA)), batchSend);
            } else {
                EmailSupporter.sendEmail(javaMailSender, emailServer, message, receiverAddressList, batchSend);
            }
        } else {
            this.umsSendEmail(receiverAddressList, message, messageSender);
        }
    }



    /**
     *
     * 邮件发送接入ums
     *  注：如果收件人存在内部用户和外部用户时候，内部用户只能密送、
     *     抄送的时候收件人存在内部和外部时候，内部用户只能密送
     * <AUTHOR>
     * @param receiverAddressList 用户组中维护的接收人
     * @param message 邮件对象
     * @param messageSender 入参
     */
    private void umsSendEmail(List<String> receiverAddressList, Message message,MessageSender messageSender) throws Exception {
        //获取附件信息
        List<Attachment> attachmentList = message.getAttachmentList();
        List<EmailAttachmentDTO> emailAttachmentList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(attachmentList)) {
            for (Attachment attachment : attachmentList) {
                EmailAttachmentDTO emailAttachment = new EmailAttachmentDTO();
                emailAttachment.setContent(Base64.getEncoder().encodeToString(attachment.getFile()));
                emailAttachment.setName(attachment.getFileName());
                emailAttachmentList.add(emailAttachment);
            }
        }
        EmailRequestVO emailRequestVO = new EmailRequestVO();
        emailRequestVO.setAppId(this.messageConfig.getAppId());
        emailRequestVO.setBizId(message.getMessageId().toString());
        emailRequestVO.setContent(message.getContent());
        //开发测试环境不会真的发送邮件，所以开发和测试配置一个默认的测试邮箱
        if (messageConfig.getTestMailbox()) {
            if(StringUtils.isEmpty(messageConfig.getTestMail())){
                throw new CommonException("current is test model, but the test email is null.");
            }
            //收件人
            emailRequestVO.setRecipients(null);
            //密送  https://doc.djicorp.com/view/3ae188c3-31f3-4813-a8a3-e013b635356a#%E9%82%AE%E4%BB%B6%E5%B9%B3%E5%8F%B0%E9%94%99%E8%AF%AF%E7%A0%81%EF%BC%8C%E5%8F%8A%E5%BA%94%E5%AF%B9%E7%AD%96%E7%95%A5
            // 当存在内部和外部人员时，内部人员只能是密送(收件人),请把公司内部的收件人写到BCC
            emailRequestVO.setBcc(Arrays.asList(messageConfig.getTestMail().split(BaseConstants.Symbol.COMMA)));
            //抄送
            emailRequestVO.setCc(null);
        } else {
            //收件人
            emailRequestVO.setRecipients(receiverAddressList);
            List<String> bccList = CollectionUtils.isNotEmpty(message.getBccList()) ? message.getBccList() : new ArrayList<>();
            //密送
            emailRequestVO.setBcc(bccList);
            //抄送
            emailRequestVO.setCc(message.getCcList());
        }
        emailRequestVO.setTitle(message.getSubject());
        //附件
        if (CollectionUtils.isNotEmpty(emailAttachmentList)) {
            emailRequestVO.setAttachList(emailAttachmentList);
        }
        //部分模块需要自定义发件人，为空的话走默认
        if (StringUtils.hasText(messageSender.getSender())) {
            emailRequestVO.setSender(messageSender.getSender());
        }
        String payload = JSON.toJSONString(emailRequestVO);
        logger.info("Email payload:{}", payload);
        GatewayClient client = new GatewayClient(this.messageConfig.getUrl(), this.messageConfig.getGwId(), this.messageConfig.getGwSecret());
        String invokeUrl = SpfmConstants.InvokeUrl.EMAIL_SEND_POST;
        Map<String, String> headers = new HashMap<String, String>();
        Response respPost = client.httpPost(invokeUrl, "", headers, payload, "json");
        logger.info("Email results:{}", respPost);
        if (respPost.getStatus() != SUCCESS_CODE) {
            logger.error("Mail sending status {},error message {}", respPost.getStatus(), JSON.parseObject(respPost.getContent()).get("resultText"));
            throw new CommonException("Mail sending error message :" + JSON.parseObject(respPost.getContent()).get("resultText"));
        }
        String content = respPost.getContent();
        if ((Integer) JSON.parseObject(content).get(RESULT_CODE) != 0) {
            logger.error("Email error message {}", JSON.parseObject(content).get("resultText"));
            throw new CommonException("Email error message :" + JSON.parseObject(content).get("resultText"));
        }
    }

}
