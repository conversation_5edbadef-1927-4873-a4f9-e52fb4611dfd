

package org.hzero.message.infra.mapper;

import io.choerodon.mybatis.common.BaseMapper;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.hzero.message.api.dto.NoticeDTO;
import org.hzero.message.domain.entity.Notice;

public interface NoticeMapper extends BaseMapper<Notice>{
    List<NoticeDTO> selectNotice(NoticeDTO noticeDTO);

    List<NoticeDTO> listUserAnnouncement(NoticeDTO noticeDTO);

    List<NoticeDTO> selectNoticeTitle(@Param("title") String title, @Param("lang") String lang, @Param("noticeCategoryCode") String noticeCategoryCode, @Param("now") Date now, @Param("tenantId") Long tenantId);

    NoticeDTO selectNoticeBody(@Param("tenantId") Long tenantId, @Param("noticeId") Long noticeId);

    NoticeDTO selectNoticeById(@Param("tenantId") Long tenantId, @Param("noticeId") Long noticeId);

    NoticeDTO getNoticeByUser(NoticeDTO noticeDTO);
}
