package org.hzero.message.app.service.impl;

import com.alibaba.fastjson.*;
import lombok.extern.slf4j.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.hzero.boot.message.entity.MessageSender;
import org.hzero.message.app.service.DjiSendEmailService;
import org.hzero.message.app.service.EmailSendService;
import org.hzero.message.domain.entity.Message;
import org.hzero.message.domain.entity.MessageTemplate;
import org.hzero.message.domain.repository.MessageTemplateRepository;
import org.hzero.message.infra.feign.DjiMarketingFeign;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * description
 *
 * <AUTHOR> 2021/03/19 16:13
 */
@Slf4j
@Service
public class DjiSendEmailServiceImpl implements DjiSendEmailService {

	@Autowired
	private EmailSendService emailSendService;
	@Autowired
	private MessageTemplateRepository messageTemplateRepository;
	@Autowired
	private DjiMarketingFeign djiMarketingFeign;

	@Override
	public Message sendMessage(List<MessageSender> list) {
		log.info("send email message{}", JSON.toJSONString(list));
		if (CollectionUtils.isEmpty(list)) {
			return null;
		}
		Message message = new Message();
		try {
			for (MessageSender messageSender : list) {
				message = emailSendService.sendMessage(messageSender);
			}
		} catch (Exception e) {
			//上线后的更改，为了不影响其他模块的使用，
			//批量发送失败后将发送标识改为0，且将错误信息返回到content
			message.setSendFlag(0);
			message.setContent(e.getMessage());
		}
		return message;
	}

	@Override
	public org.hzero.boot.message.entity.MessageTemplate MessageTemplate(Long tenantId , String templateCode) {
		if (StringUtils.isEmpty(templateCode)) {
			return null;
		}
		org.hzero.boot.message.entity.MessageTemplate messageTemplates = new org.hzero.boot.message.entity.MessageTemplate();
		MessageTemplate selectCondition = (new MessageTemplate()).setTenantId(tenantId).setTemplateCode(templateCode);
		MessageTemplate messageTemplate = messageTemplateRepository.selectOne(selectCondition);
		if (Objects.nonNull(messageTemplate)){
			BeanUtils.copyProperties(messageTemplate,messageTemplates);
		}
		return messageTemplates;
	}
}
