package org.hzero.boot.platform.export;

import com.alibaba.fastjson.*;
import org.hzero.boot.platform.export.feign.*;
import org.hzero.core.async.*;
import org.hzero.core.export.*;
import org.hzero.message.domain.entity.*;
import org.hzero.message.infra.feign.*;
import org.slf4j.*;
import org.springframework.beans.factory.annotation.*;
import org.springframework.http.*;

import java.util.*;

public class DefaultExportAsyncTemplate implements ExportAsyncTemplate {
    private static final Logger logger = LoggerFactory.getLogger(DefaultExportAsyncTemplate.class);

    public static final String DIRECTORY = "export-async/order";
    public static final String STORAGE_CODE = "O2-PRIVITE";
    private static final String ZIP_SUFFIX = ".zip";
    private static final String EXCEL_SUFFIX = ".xlsx";
    @Autowired
    private ExportTaskService exportTaskService;
    @Autowired
    private DjiFileServiceFeign fileClient;

    public DefaultExportAsyncTemplate() {
    }

    @Override
    public void afterSubmit(ExportTaskDTO dto) {
        dto.setState(AsyncTaskState.DOING);
        this.exportTaskService.insert(dto);
    }

    @Override
    public Object doWhenFinish(ExportTaskDTO dto, Map<String, Object> additionInfo) {
        String fileType = (String) additionInfo.get("fileType");
        byte[] file = (byte[]) additionInfo.get("file");
        String fileSuffix = "excel".equals(fileType) ? EXCEL_SUFFIX : ZIP_SUFFIX;
        String fileTypeParam = "excel".equals(fileType) ? "application/vnd.ms-excel" : "application/zip";
        logger.info("begin upload file taskName is {}", dto.getTaskName());
        ResponseEntity<DjiFile> fileResponseEntity = this.fileClient.file2Id(dto.getTenantId(), "private", DIRECTORY, dto.getTaskName() + fileSuffix, fileTypeParam, STORAGE_CODE, file);
        logger.info("current fileClient response，{}", JSONObject.toJSONString(fileResponseEntity));
        String downloadUrl = "";
        if (null != fileResponseEntity) {
            DjiFile body = fileResponseEntity.getBody();
            logger.info("export result:{}", JSON.toJSONString(body));
            if (null != body) {
                downloadUrl = body.getFileKey();
            }
        }
        dto.setState(AsyncTaskState.DONE);
        dto.setDownloadUrl(downloadUrl);
        dto.setEndDateTime(new Date());
        logger.info("export Task :{}", JSON.toJSONString(dto));
        this.exportTaskService.update(dto);
        return null;
    }

    @Override
    public Object doWhenOccurException(ExportTaskDTO dto, Throwable e) {
        dto.setState(AsyncTaskState.DONE);
        dto.setErrorInfo(e.getMessage());
        dto.setEndDateTime(new Date());
        this.exportTaskService.update(dto);
        return null;
    }

}

