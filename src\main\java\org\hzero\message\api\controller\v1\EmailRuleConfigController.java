package org.hzero.message.api.controller.v1;

import io.choerodon.core.domain.*;
import io.choerodon.core.iam.*;
import io.choerodon.mybatis.domain.*;
import io.choerodon.mybatis.pagehelper.annotation.*;
import io.choerodon.mybatis.pagehelper.domain.*;
import io.choerodon.swagger.annotation.*;
import io.swagger.annotations.*;
import lombok.*;
import lombok.experimental.*;
import lombok.extern.slf4j.*;
import org.hzero.core.base.*;
import org.hzero.core.cache.*;
import org.hzero.core.util.*;
import org.hzero.export.annotation.*;
import org.hzero.export.vo.*;
import org.hzero.message.api.dto.*;
import org.hzero.message.app.service.*;
import org.hzero.message.config.*;
import org.hzero.message.domain.entity.*;
import org.hzero.message.domain.vo.*;
import org.springframework.http.*;
import org.springframework.validation.annotation.*;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.*;

import javax.servlet.http.*;
import java.util.*;

/**
 * 邮件规则配置表 管理 API
 *
 * <AUTHOR> 2024-06-14 15:38:08
 */
@Api(tags = DjiMessageSwaggerApiConfig.EMAIL_RULE_CONFIG)
@Validated
@Slf4j
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RestController("emailRuleConfigController.v1")
@RequestMapping("/v1/email-rule-config")
public class EmailRuleConfigController extends BaseController {

    private final EmailRuleConfigService emailRuleConfigService;

    @ApiOperation(value = "邮件规则配置表列表")
    @Permission(level = ResourceLevel.ORGANIZATION)
    @GetMapping("/queryEmailRuleConfigListPage")
    @ProcessCacheValue
    public ResponseEntity<Page<EmailRuleConfigResVO>> queryEmailRuleConfigListPage(EmailRuleConfigQueryDTO queryDTO,
                                                                                   @ApiIgnore @SortDefault(value = AuditDomain.FIELD_LAST_UPDATE_DATE,
                                                                                           direction = Sort.Direction.DESC) PageRequest pageRequest) {
        return Results.success(emailRuleConfigService.queryEmailRuleConfigListPage(pageRequest, queryDTO));
    }

    @ApiOperation(value = "邮件规则配置表明细")
    @Permission(level = ResourceLevel.ORGANIZATION)
    @GetMapping("/detail")
    public ResponseEntity<EmailRuleConfigResVO> detail(@RequestParam(name = "ruleConfigId", required = true) String ruleConfigId) {
        return Results.success(emailRuleConfigService.findEmailRuleConfigDetail(ruleConfigId));
    }

    @ApiOperation(value = "创建邮件规则配置表")
    @Permission(level = ResourceLevel.ORGANIZATION)
    @PostMapping("/create")
    public ResponseEntity<String> create(@RequestBody @Validated EmailRuleConfigSaveDTO saveDTO) {
        return Results.success(emailRuleConfigService.create(saveDTO));
    }

    @ApiOperation(value = "修改邮件规则配置表")
    @Permission(level = ResourceLevel.ORGANIZATION)
    @PostMapping("/update")
    public ResponseEntity<String> update(@RequestBody @Validated EmailRuleConfigUpdateDTO updateDTO) {
        return Results.success(emailRuleConfigService.update(updateDTO));
    }

    @ApiOperation(value = "删除邮件规则配置表")
    @Permission(level = ResourceLevel.ORGANIZATION)
    @GetMapping("/remove")
    public ResponseEntity<Boolean> remove(@RequestParam(name = "ruleConfigId", required = true) String ruleConfigId) {
        return Results.success(emailRuleConfigService.remove(ruleConfigId));
    }

    @ApiOperation(value = "导出邮件规则配置")
    @Permission(level = ResourceLevel.ORGANIZATION)
    @GetMapping("/export")
    @ExcelExport(EmailRuleConfigExportDTO.class)
    public ResponseEntity<List<EmailRuleConfigExportDTO>> export(EmailRuleConfigQueryDTO queryDTO, ExportParam exportParam, HttpServletResponse response) {
        return Results.success(emailRuleConfigService.export(queryDTO));
    }

    @ApiOperation(value = "发送邮件通用接口")
    @Permission(level = ResourceLevel.ORGANIZATION)
    @PostMapping("/sendMailGeneral")
    public ResponseEntity<Message> sendMailGeneral(@RequestBody @Validated SendMailGeneralDTO sendMailGeneral) {
        return Results.success(emailRuleConfigService.sendMailGeneral(sendMailGeneral));
    }

    @ApiOperation(value = "获取邮件规则及配置模版具体内容信息")
    @Permission(permissionPublic = true)
    @PostMapping("/getEmailRuleAndTemplateInfo")
    public ResponseEntity<List<EmailConfigTemplateInfoVO>> getEmailRuleAndTemplateInfo(@RequestBody List<EmailRuleConfigCmQueryDTO> emailRuleConfigQueryDTOList) {
        return Results.success(emailRuleConfigService.getEmailRuleAndTemplateInfo(emailRuleConfigQueryDTOList));
    }

}
