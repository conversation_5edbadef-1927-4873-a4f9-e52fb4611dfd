package org.hzero.message.api.controller.v1;

import io.choerodon.core.iam.ResourceLevel;
import io.choerodon.swagger.annotation.Permission;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.hzero.core.util.Results;
import org.hzero.message.api.dto.NoticeDTO;
import org.hzero.message.app.service.NoticeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Auther: jenson.wang
 * @Date: 2022/04/08/21:38
 * @Description: 弹窗推送
 */
@Api(
        tags = {"Bulletin_Publish"}
)
@RestController("noticePublishToUserController.v1")
@RequestMapping({"/v1/notices"})
public class BulletinController {


    @Autowired
    private NoticeService noticeService;


    @ApiOperation("获取最新需要弹窗的公告")
    @Permission(
            level = ResourceLevel.ORGANIZATION
    )
    @GetMapping("/{organizationId}/getBulletin")
    public ResponseEntity<NoticeDTO> bulletin(@PathVariable Long organizationId) {
        return Results.success(this.noticeService.bulletin());
    }
}
