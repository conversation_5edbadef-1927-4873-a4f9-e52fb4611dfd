package org.hzero.message.infra.feign;


import org.hzero.message.domain.entity.*;
import org.hzero.message.infra.feign.impl.*;
import org.springframework.cloud.openfeign.*;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;

/**
 * feign调用文件服务
 *
 * <AUTHOR>
 */
@FeignClient(value = "${hzero.service.file.name:hzero-file}", fallbackFactory = DjiFileServiceFeignFallback.class)
public interface DjiFileServiceFeign {

    /**
     * file v3 附件上传
     *
     * @param organizationId 租户ID
     * @param bucketName 桶名
     * @param directory 上传目录
     * @param fileName 文件名
     * @param fileType 文件类型
     * @param storageCode 存储配置编码
     * @param byteFile 上传文件
     * <AUTHOR> 2020/11/30 14:23
     * @return org.springframework.http.ResponseEntity<com.dji.product.console.domain.entity.DjiFile>
     **/
    @PostMapping("/v3/{organizationId}/attach/file/upload")
    ResponseEntity<DjiFile> file2Id(@PathVariable Long organizationId, @RequestParam("bucketName") String bucketName,
                                    @RequestParam(value = "directory", required = false) String directory,
                                    @RequestParam("fileName") String fileName,
                                    @RequestParam(value = "fileType", required = false) String fileType,
                                    @RequestParam(value = "storageCode", required = false) String storageCode,
                                    @RequestBody byte[] byteFile);


}
