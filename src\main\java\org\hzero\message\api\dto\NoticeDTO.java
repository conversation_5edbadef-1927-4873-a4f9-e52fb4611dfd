

package org.hzero.message.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import org.hzero.boot.platform.lov.annotation.LovValue;
import org.hzero.core.cache.CacheValue;
import org.hzero.core.cache.Cacheable;
import org.hzero.core.cache.CacheValue.DataStructure;
import org.hzero.message.domain.entity.Notice;
import org.hzero.message.domain.entity.NoticeContent;
import org.hzero.mybatis.domian.SecurityToken;
import org.hzero.starter.keyencrypt.core.Encrypt;
import org.springframework.format.annotation.DateTimeFormat;

@ApiModel("公告基础信息")
@JsonInclude(Include.NON_NULL)
public class NoticeDTO extends Notice implements Cacheable, SecurityToken {
    @Encrypt
    @ApiModelProperty("表ID，主键，供其他表做外键")
    private Long noticeId;
    @ApiModelProperty("语言code")
    @LovValue(
            lovCode = "HPFM.LANGUAGE"
    )
    private String lang;
    @ApiModelProperty("公告主题")
    private String title;
    @ApiModelProperty("接收方类型,值集：HMSG.NOTICE.RECERVER_TYPE")
    @LovValue(
            lovCode = "HMSG.NOTICE.RECERVER_TYPE"
    )
    private String receiverTypeCode;
    @ApiModelProperty("公告类别,值集：HMSG.NOTICE.NOTICE_CATEGORY")
    @LovValue(
            lovCode = "HMSG.NOTICE.NOTICE_CATEGORY"
    )
    private String noticeCategoryCode;
    @ApiModelProperty("公告类型,值集：HMSG.NOTICE.NOTICE_TYPE.CH")
    @LovValue(
            lovCode = "HMSG.NOTICE.NOTICE_TYPE.CH"
    )
    private String noticeTypeCode;
    @ApiModelProperty("提醒方式,值集：HMSG.NOTIFICATION_TYPE")
    @LovValue(
            lovCode = "HMSG.NOTIFICATION_TYPE"
    )
    private String remindTypeCode;
    @ApiModelProperty("有效期从")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date startDate;
    @ApiModelProperty("有效期至")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date endDate;
    @ApiModelProperty("租户ID")
    private Long tenantId;
    @ApiModelProperty("公告状态，值集：HMSG.NOTICE.STATUS")
    @LovValue(
            lovCode = "HMSG.NOTICE.STATUS"
    )
    private String statusCode;
    @ApiModelProperty("公告内容")
    private NoticeContent noticeContent;
    @ApiModelProperty("版本编号")
    private Long objectVersionNumber;
    @ApiModelProperty("附件uuid")
    private String attachmentUuid;
    @ApiModelProperty("发布时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date publishedDate;
    @ApiModelProperty("发布人ID")
    private Long publishedBy;
    @ApiModelProperty("发布日期从")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd"
    )
    private Date publishedDateFrom;
    @ApiModelProperty("发布日期至")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd"
    )
    private Date publishedDateTo;
    @ApiModelProperty("包含删除数据，1包含，0或者空不包含")
    private Integer containsDeletedDataFlag;
    private String receiverTypeMeaning;
    private String noticeCategoryMeaning;
    private String noticeTypeMeaning;
    private String statusMeaning;
    private String langMeaning;
    private String noticeBody;
    private String secondBody;
    private Long tempPublishedBy;
    @CacheValue(
            key = "hiam:user",
            primaryKey = "tempPublishedBy",
            searchKey = "realName",
            structure = DataStructure.MAP_OBJECT,
            db = 1
    )
    private String publishedByUser;
    private String userNotice;
    private Long userId;
    private Integer previewCount;
    @ApiModelProperty("创建时间从")
    private Date fromDate;
    @ApiModelProperty("创建时间至")
    private Date toDate;
    @ApiModelProperty("消息标题")
    private String subject;
    @ApiModelProperty("当前时间")
    private Date now;

    public NoticeDTO() {
    }

    public Date getNow() {
        return now;
    }

    public void setNow(Date now) {
        this.now = now;
    }

    public Long getNoticeId() {
        return this.noticeId;
    }

    public NoticeDTO setNoticeId(Long noticeId) {
        this.noticeId = noticeId;
        return this;
    }

    public String getLang() {
        return this.lang;
    }

    public NoticeDTO setLang(String lang) {
        this.lang = lang;
        return this;
    }

    public String getTitle() {
        return this.title;
    }

    public NoticeDTO setTitle(String title) {
        this.title = title;
        return this;
    }

    public String getReceiverTypeCode() {
        return this.receiverTypeCode;
    }

    public NoticeDTO setReceiverTypeCode(String receiverTypeCode) {
        this.receiverTypeCode = receiverTypeCode;
        return this;
    }

    public String getNoticeCategoryCode() {
        return this.noticeCategoryCode;
    }

    public NoticeDTO setNoticeCategoryCode(String noticeCategoryCode) {
        this.noticeCategoryCode = noticeCategoryCode;
        return this;
    }

    public String getNoticeTypeCode() {
        return this.noticeTypeCode;
    }

    public NoticeDTO setNoticeTypeCode(String noticeTypeCode) {
        this.noticeTypeCode = noticeTypeCode;
        return this;
    }

    public Date getStartDate() {
        return this.startDate;
    }

    public NoticeDTO setStartDate(Date startDate) {
        this.startDate = startDate;
        return this;
    }

    public Date getEndDate() {
        return this.endDate;
    }

    public NoticeDTO setEndDate(Date endDate) {
        this.endDate = endDate;
        return this;
    }

    public Long getTenantId() {
        return this.tenantId;
    }

    public NoticeDTO setTenantId(Long tenantId) {
        this.tenantId = tenantId;
        return this;
    }

    public String getStatusCode() {
        return this.statusCode;
    }

    public NoticeDTO setStatusCode(String statusCode) {
        this.statusCode = statusCode;
        return this;
    }

    public NoticeContent getNoticeContent() {
        return this.noticeContent;
    }

    public NoticeDTO setNoticeContent(NoticeContent noticeContent) {
        this.noticeContent = noticeContent;
        return this;
    }

    public Long getObjectVersionNumber() {
        return this.objectVersionNumber;
    }

    public void setObjectVersionNumber(Long objectVersionNumber) {
        this.objectVersionNumber = objectVersionNumber;
    }

    public String getAttachmentUuid() {
        return this.attachmentUuid;
    }

    public NoticeDTO setAttachmentUuid(String attachmentUuid) {
        this.attachmentUuid = attachmentUuid;
        return this;
    }

    public Date getPublishedDate() {
        return this.publishedDate;
    }

    public NoticeDTO setPublishedDate(Date publishedDate) {
        this.publishedDate = publishedDate;
        return this;
    }

    public Long getPublishedBy() {
        return this.publishedBy;
    }

    public NoticeDTO setPublishedBy(Long publishedBy) {
        this.publishedBy = publishedBy;
        return this;
    }

    public Date getPublishedDateFrom() {
        return this.publishedDateFrom;
    }

    public NoticeDTO setPublishedDateFrom(Date publishedDateFrom) {
        this.publishedDateFrom = publishedDateFrom;
        return this;
    }

    public Date getPublishedDateTo() {
        return this.publishedDateTo;
    }

    public NoticeDTO setPublishedDateTo(Date publishedDateTo) {
        this.publishedDateTo = publishedDateTo;
        return this;
    }

    public Integer getContainsDeletedDataFlag() {
        return this.containsDeletedDataFlag;
    }

    public NoticeDTO setContainsDeletedDataFlag(Integer containsDeletedDataFlag) {
        this.containsDeletedDataFlag = containsDeletedDataFlag;
        return this;
    }

    public String getReceiverTypeMeaning() {
        return this.receiverTypeMeaning;
    }

    public NoticeDTO setReceiverTypeMeaning(String receiverTypeMeaning) {
        this.receiverTypeMeaning = receiverTypeMeaning;
        return this;
    }

    public String getNoticeCategoryMeaning() {
        return this.noticeCategoryMeaning;
    }

    public NoticeDTO setNoticeCategoryMeaning(String noticeCategoryMeaning) {
        this.noticeCategoryMeaning = noticeCategoryMeaning;
        return this;
    }

    public String getNoticeTypeMeaning() {
        return this.noticeTypeMeaning;
    }

    public NoticeDTO setNoticeTypeMeaning(String noticeTypeMeaning) {
        this.noticeTypeMeaning = noticeTypeMeaning;
        return this;
    }

    public String getStatusMeaning() {
        return this.statusMeaning;
    }

    public NoticeDTO setStatusMeaning(String statusMeaning) {
        this.statusMeaning = statusMeaning;
        return this;
    }

    public String getLangMeaning() {
        return this.langMeaning;
    }

    public NoticeDTO setLangMeaning(String langMeaning) {
        this.langMeaning = langMeaning;
        return this;
    }

    public String getNoticeBody() {
        return this.noticeBody;
    }

    public NoticeDTO setNoticeBody(String noticeBody) {
        this.noticeBody = noticeBody;
        return this;
    }

    public String getSecondBody() {
        return secondBody;
    }

    public void setSecondBody(String secondBody) {
        this.secondBody = secondBody;
    }

    public Long getTempPublishedBy() {
        return this.tempPublishedBy;
    }

    public NoticeDTO setTempPublishedBy(Long tempPublishedBy) {
        this.tempPublishedBy = tempPublishedBy;
        return this;
    }

    public String getPublishedByUser() {
        return this.publishedByUser;
    }

    public NoticeDTO setPublishedByUser(String publishedByUser) {
        this.publishedByUser = publishedByUser;
        return this;
    }

    public String getUserNotice() {
        return this.userNotice;
    }

    public NoticeDTO setUserNotice(String userNotice) {
        this.userNotice = userNotice;
        return this;
    }

    public Long getUserId() {
        return this.userId;
    }

    public NoticeDTO setUserId(Long userId) {
        this.userId = userId;
        return this;
    }

    public Integer getPreviewCount() {
        return this.previewCount;
    }

    public NoticeDTO setPreviewCount(Integer previewCount) {
        this.previewCount = previewCount;
        return this;
    }

    public Date getFromDate() {
        return this.fromDate;
    }

    public NoticeDTO setFromDate(Date fromDate) {
        this.fromDate = fromDate;
        return this;
    }

    public Date getToDate() {
        return this.toDate;
    }

    public NoticeDTO setToDate(Date toDate) {
        this.toDate = toDate;
        return this;
    }

    public String getSubject() {
        return this.subject;
    }

    public NoticeDTO setSubject(String subject) {
        this.subject = subject;
        return this;
    }

    public String getRemindTypeCode() {
        return remindTypeCode;
    }

    public void setRemindTypeCode(String remindTypeCode) {
        this.remindTypeCode = remindTypeCode;
    }

    @Override
    public String toString() {
        return "NoticeDTO{" +
                "noticeId=" + noticeId +
                ", lang='" + lang + '\'' +
                ", title='" + title + '\'' +
                ", receiverTypeCode='" + receiverTypeCode + '\'' +
                ", noticeCategoryCode='" + noticeCategoryCode + '\'' +
                ", noticeTypeCode='" + noticeTypeCode + '\'' +
                ", remindTypeCode='" + remindTypeCode + '\'' +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                ", tenantId=" + tenantId +
                ", statusCode='" + statusCode + '\'' +
                ", noticeContent=" + noticeContent +
                ", objectVersionNumber=" + objectVersionNumber +
                ", attachmentUuid='" + attachmentUuid + '\'' +
                ", publishedDate=" + publishedDate +
                ", publishedBy=" + publishedBy +
                ", publishedDateFrom=" + publishedDateFrom +
                ", publishedDateTo=" + publishedDateTo +
                ", containsDeletedDataFlag=" + containsDeletedDataFlag +
                ", receiverTypeMeaning='" + receiverTypeMeaning + '\'' +
                ", noticeCategoryMeaning='" + noticeCategoryMeaning + '\'' +
                ", noticeTypeMeaning='" + noticeTypeMeaning + '\'' +
                ", statusMeaning='" + statusMeaning + '\'' +
                ", langMeaning='" + langMeaning + '\'' +
                ", noticeBody='" + noticeBody + '\'' +
                ", tempPublishedBy=" + tempPublishedBy +
                ", publishedByUser='" + publishedByUser + '\'' +
                ", userNotice='" + userNotice + '\'' +
                ", userId=" + userId +
                ", previewCount=" + previewCount +
                ", fromDate=" + fromDate +
                ", toDate=" + toDate +
                ", subject='" + subject + '\'' +
                '}';
    }

    public Class<? extends SecurityToken> associateEntityClass() {
        return Notice.class;
    }
}
