package org.hzero.message.api.dto;

import com.google.common.collect.*;
import io.swagger.annotations.*;
import lombok.*;
import lombok.experimental.*;
import org.apache.commons.lang3.*;
import org.hzero.boot.message.entity.*;
import org.hzero.message.domain.entity.*;

import javax.validation.constraints.*;
import java.util.*;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@FieldDefaults(level = AccessLevel.PRIVATE)
@ApiModel("发送邮件通用DTO")
public class SendMailGeneralDTO {

    @ApiModelProperty(value = "场景id")
    @NotBlank(message = "场景id不能为空")
    String sceneId;

    @ApiModelProperty(value = "组别id")
    @NotNull(message = "组别id不能为空")
    Long groupId;

    @ApiModelProperty(value = "语种:中文zh-CN,英文en-US,日文ja-JP")
    @NotBlank(message = "语种不能为空")
    String language;

    @ApiModelProperty(value = "接收人")
    @NotEmpty(message = "接收人不能为空")
    List<Receiver> receiverAddressList;

    @ApiModelProperty(value = "附件")
    List<Attachment> attachmentList;

    @ApiModelProperty(value = "对象参数")
    Map<String, Object> objectArgs;

    @ApiModelProperty(value = "对象参数")
    Map<String, String> args;

    @ApiModelProperty(value = "抄送")
    List<String> ccList;

    @ApiModelProperty(value = "密送")
    List<String> bccList;

    @ApiModelProperty(value = "发件人,通常是公共帐号，不指定的情况下默认为ums-msg，即ums<EMAIL>,")
    private String sender;

    public MessageSender buildMessageSender(EmailRuleConfig emailRuleConfig){
        MessageSender messageSender = new MessageSender();
        messageSender.setReceiverAddressList(this.receiverAddressList);
        messageSender.setTenantId(0L);
        messageSender.setAttachmentList(this.attachmentList);
        messageSender.setCcList(this.ccList);
        messageSender.setBccList(this.bccList);
        messageSender.setMessageCode(emailRuleConfig.getTemplateCode());
        messageSender.setServerCode(emailRuleConfig.getSendMailBox());
        messageSender.setLang(emailRuleConfig.getLanguage().contains("en") || emailRuleConfig.getLanguage().contains("ja") ? emailRuleConfig.getLanguage().replace("-", "_") : "zh_CN");
        messageSender.setSender(StringUtils.isNotBlank(this.sender) ? this.sender : null);
        Map<String, Object> objectArgs = new HashMap<>();
        if (Objects.nonNull(this.getArgs()) && !this.getArgs().isEmpty()) {
            objectArgs.putAll(this.getArgs());
        }
        if (Objects.nonNull(this.getObjectArgs()) && !this.getObjectArgs().isEmpty()) {
            objectArgs.putAll(this.getObjectArgs());
        }
        if (StringUtils.isNotBlank(emailRuleConfig.getSignName())) {
            objectArgs.put("signName", emailRuleConfig.getSignName());
        }
        messageSender.setObjectArgs(objectArgs);
        return messageSender;
    }


}
