package org.hzero.message.domain.entity;

import com.fasterxml.jackson.annotation.*;
import com.fasterxml.jackson.annotation.JsonInclude.*;
import io.choerodon.mybatis.annotation.*;
import io.choerodon.mybatis.domain.*;
import io.swagger.annotations.*;
import lombok.*;
import org.hibernate.validator.constraints.*;
import org.hzero.boot.platform.lov.annotation.*;
import org.hzero.starter.keyencrypt.core.*;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.*;

@EqualsAndHashCode(callSuper = true)
@Data
@VersionAudit
@ModifyAudit
@JsonInclude(Include.NON_NULL)
public class DjiFile extends AuditDomain {
    public static final String FIELD_FILE_ID = "fileId";
    public static final String FIELD_ATTACHMENT_UUID = "attachmentUuid";
    public static final String FIELD_DIRECTORY = "directory";
    public static final String FIELD_FILE_URL = "fileUrl";
    public static final String FIELD_FILE_TYPE = "fileType";
    public static final String FIELD_FILE_NAME = "fileName";
    public static final String FIELD_FILE_SIZE = "fileSize";
    public static final String FIELD_BUCKET_NAME = "bucketName";
    public static final String FIELD_FILE_KEY = "fileKey";
    public static final String FIELD_TENANT_ID = "tenantId";
    public static final String FIELD_MD5 = "md5";
    public static final String FIELD_STORAGE_CODE = "storageCode";
    public static final String FIELD_SERVER_CODE = "serverCode";
    @ApiModelProperty("表ID，主键，供其他表做外键")
    @Encrypt
    private Long fileId;
    @Length(
            max = 50
    )
    @ApiModelProperty("附件集UUID")
    private String attachmentUuid;
    @Length(
            max = 60
    )
    @ApiModelProperty("上传目录")
    private String directory;
    @Length(
            max = 480
    )
    @ApiModelProperty("文件地址")
    private String fileUrl;
    @Length(
            max = 120
    )
    @ApiModelProperty("文件类型")
    private String fileType;
    @ApiModelProperty("文件名称")
    @Length(
            max = 240
    )
    private String fileName;
    @ApiModelProperty("文件大小")
    private Long fileSize;
    @NotBlank(
            message = "文件Bucket不能为空"
    )
    @Length(
            max = 60
    )
    @ApiModelProperty("文件目录")
    private String bucketName;
    @ApiModelProperty("对象KEY")
    @Length(
            max = 480
    )
    private String fileKey;
    @NotNull(
            message = "租户不能为空"
    )
    @ApiModelProperty("租户Id")
    private Long tenantId;
    @Length(
            max = 60
    )
    @ApiModelProperty("文件MD5")
    private String md5;
    @ApiModelProperty("存储编码")
    private String storageCode;
    @ApiModelProperty("来源类型")
    @LovValue("HFLE.SERVER_PROVIDER")
    private String sourceType;
    @ApiModelProperty("服务器编码，hpfm_server.server_code")
    @Pattern(
            regexp = "^[A-Z0-9][A-Z0-9-_./]*$"
    )
    private String serverCode;
    private String tenantName;
    private String realName;
    private String sourceTypeMeaning;
    private String tableName;
}

