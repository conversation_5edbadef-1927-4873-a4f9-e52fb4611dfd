//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by <PERSON><PERSON>flower decompiler)
//

package org.hzero.message.domain.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.choerodon.mybatis.annotation.ModifyAudit;
import io.choerodon.mybatis.annotation.VersionAudit;
import io.choerodon.mybatis.domain.AuditDomain;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotNull;
import org.hzero.starter.keyencrypt.core.Encrypt;

@ApiModel("公告具体内容")
@VersionAudit
@ModifyAudit
@Table(
    name = "hmsg_notice_content"
)
@JsonInclude(Include.NON_NULL)
public class NoticeContent extends AuditDomain {
    public static final String FIELD_NOTICE_CONTENT_ID = "noticeContentId";
    public static final String FIELD_NOTICE_ID = "noticeId";
    public static final String FIELD_NOTICE_BODY = "noticeBody";
    public static final String FIELD_SECOND_BODY = "secondBody";
    public static final String FIELD_TENANT_ID = "tenantId";
    public static final String FIELD_RECEIVE_TENANT_ID = "receiveTenantId";
    public static final String FIELD_RECV_USER_GROUP_ID = "recvUserGroupId";
    public static final String FIELD_RECEIVE_USER_ID = "receiveUserId";
    public static final String FIELD_RECV_TENANT_NAME = "recvTenantName";
    public static final String FIELD_RECV_GROUP_NAME = "recvGroupName";
    public static final String FIELD_RECV_USER_NAME = "receiveUserName";
    @ApiModelProperty("表ID，主键，供其他表做外键")
    @Id
    @GeneratedValue
    @Encrypt
    private Long noticeContentId;
    @ApiModelProperty("公告头ID")
    @NotNull
    @Encrypt
    private Long noticeId;
    private String noticeBody;
    private String secondBody;
    @ApiModelProperty("租户ID")
    @NotNull
    private Long tenantId;

    public NoticeContent() {
    }

    public String getSecondBody() {
        return secondBody;
    }

    public void setSecondBody(String secondBody) {
        this.secondBody = secondBody;
    }

    public Long getNoticeContentId() {
        return this.noticeContentId;
    }

    public NoticeContent setNoticeContentId(Long noticeContentId) {
        this.noticeContentId = noticeContentId;
        return this;
    }

    public Long getNoticeId() {
        return this.noticeId;
    }

    public NoticeContent setNoticeId(Long noticeId) {
        this.noticeId = noticeId;
        return this;
    }

    public String getNoticeBody() {
        return this.noticeBody;
    }

    public NoticeContent setNoticeBody(String noticeBody) {
        this.noticeBody = noticeBody;
        return this;
    }

    public Long getTenantId() {
        return this.tenantId;
    }

    public NoticeContent setTenantId(Long tenantId) {
        this.tenantId = tenantId;
        return this;
    }
}
