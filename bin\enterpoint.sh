#!/bin/bash

###############################
# 您需要从容器环境内获取三个变量 (若接入k8s需要事先在k8s配置好)
# confserver_seckey_${APOLLO_APP_ID}
# ENV
# CLUSTER
##############################
set -e

    #加载apollo.properties的配置值
	file="./apollo.properties"
	if [ -f "$file" ] ; then
		while IFS='=' read -r key value
		do
			key=$(echo $key | tr '.' '_')
			echo "${key} = ${value}"
			eval ${key}=\${value}
		done < "$file"
	else
		echo "$file not found."
	fi

	if [ -z $ENV ] ; then
		echo "ENV not set ,start failed"
		exit 0
	fi

	if [ -z $CLUSTER ] ; then
		echo "CLUSTER not set ,start failed"
		exit 0
	fi

    if [ -z `eval echo '$'"confserver_seckey_${APOLLO_APP_ID}"` ] ; then
        echo "confserver_seckey_${APOLLO_APP_ID} no exists,start failed"
        exit 0
    fi


	APOLLO_META=`eval echo '$'"${ENV}_${CLUSTER}"`
	env=`tr '[A-Z]' '[a-z]' <<<"$ENV"`
	APOLLO_PARAM="-Dapp.id=${APOLLO_APP_ID} -D${env}_meta=$APOLLO_META -Dlog4j.fileName=$HOSTNAME"

	# 堆设置
	JAVA_OPTS="-Xms1g -Xmx3g -XX:MetaspaceSize=256m -XX:MaxMetaspaceSize=512m"
	# GC 设置
	JAVA_OPTS="$JAVA_OPTS -XX:+UseParNewGC  -XX:MaxTenuringThreshold=9 -XX:+UseConcMarkSweepGC -XX:+UseCMSInitiatingOccupancyOnly -XX:+ScavengeBeforeFullGC -XX:+UseCMSCompactAtFullCollection -XX:+CMSParallelRemarkEnabled -XX:CMSFullGCsBeforeCompaction=9 -XX:CMSInitiatingOccupancyFraction=60 -XX:+CMSClassUnloadingEnabled -XX:SoftRefLRUPolicyMSPerMB=0 -XX:+CMSPermGenSweepingEnabled -XX:CMSInitiatingPermOccupancyFraction=70 -XX:+ExplicitGCInvokesConcurrent -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+PrintGCApplicationConcurrentTime -XX:+PrintHeapAtGC -XX:+HeapDumpOnOutOfMemoryError -XX:-OmitStackTraceInFastThrow -Dclient.encoding.override=UTF-8 -Dfile.encoding=UTF-8 -Djava.security.egd=file:/dev/./urandom"
	# GC 额外设置
	JAVA_OPTS="$JAVA_OPTS -Xloggc:./gc_%p.log -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=./heap_%p.hprof"

	echo "System Information:"
	echo "****************************"
	echo `uname -a`
	echo
	echo `java -version`
	echo
	echo "ENV = $ENV ,CLUSTER = $CLUSTER"
	echo "APOLLO_SECKEY  available"
	echo "APOLLO_APP_ID = $APOLLO_APP_ID"
	echo "APOLLO_META = $APOLLO_META"
	echo "APOLLO_PARAM = $APOLLO_PARAM"
	echo "****************************"

	# startup
	echo "ready to start:"
	echo "exec java ${APOLLO_PARAM} -jar ${JAVA_OPTS}  /usr/share/dava/application.jar --spring.profiles.active=${ENV}"
	exec java -Duser.language=en ${APOLLO_PARAM} -jar ${JAVA_OPTS}  /usr/share/dava/application.jar --spring.profiles.active=${ENV}
