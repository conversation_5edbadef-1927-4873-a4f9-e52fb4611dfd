package org.hzero.message.app.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dji.dava.gateway.cloudbus.RemoteInvoke;
import com.dji.dava.utils.JsonUtils;
import org.hzero.message.api.controller.v1.dto.SmsSendDTO;
import org.hzero.message.app.service.SmsSendV2Service;
import org.hzero.message.config.MessageConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.UUID;

/**
 * @Author: c-li.xiao
 * @Description:短信发送接入ums
 * @Date: 2025/10/16 14:18
 */
@Service
public class SmsSendV2ServiceImpl implements SmsSendV2Service {
    private static final Logger logger = LoggerFactory.getLogger(SmsSendV2ServiceImpl.class);

    @Autowired
    private MessageConfig messageConfig;
    @Autowired
    private RemoteInvoke remoteInvoke;

    @Override
    public String sendPhoneSms(SmsSendDTO smsSendDTO){
        smsSendDTO.setAppId(messageConfig.getAppId());
        smsSendDTO.setBizId(UUID.randomUUID().toString());
        String invokeUrlString = messageConfig.getSmsUrl();
        String jsonString = JSON.toJSONString(smsSendDTO);
        logger.info(String.format("向网关请求地址:%s, 请求参数为:%s", invokeUrlString, jsonString));
        String resultString = "";
        try {
            resultString = remoteInvoke.postJson(messageConfig.getSmsUrl(), JsonUtils.toJson(smsSendDTO), new HashMap<>(), new TypeReference<String>() {
            });
            logger.info(String.format("网关postJson请求返回结果--网关请求地址:%s, 请求参数为:%s, 返回结果是:%s", invokeUrlString, jsonString, resultString));
        }catch (Exception e){
            logger.error(String.format("网关postJson请求发生错误--向网关请求地址:%s, 请求参数为:%s, 发生错误:%s", invokeUrlString, jsonString, e.getMessage()));
        }
        return resultString;
    }


}
