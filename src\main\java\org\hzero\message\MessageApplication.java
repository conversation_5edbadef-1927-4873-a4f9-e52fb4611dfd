package org.hzero.message;

import com.ctrip.framework.apollo.spring.annotation.*;
import com.dji.gt.sdk.config.*;
import org.apache.logging.log4j.*;
import org.hzero.autoconfigure.message.*;
import org.springframework.boot.*;
import org.springframework.boot.autoconfigure.*;
import org.springframework.cloud.client.discovery.*;
import org.springframework.context.annotation.*;

@EnableHZeroMessage
@EnableDiscoveryClient
@SpringBootApplication
@EnableApolloConfig
@EnableGtClient
@ComponentScan({"com.dji", "org.o2", "org.hzero.message.imported"})
public class MessageApplication {

    public static final Logger LOGGER = LogManager.getLogger(MessageApplication.class);

    public static void main(String[] args) {
        SpringApplication.run(MessageApplication.class, args);
    }
}


