package org.hzero.message.app.service.impl;

import com.alibaba.fastjson.JSON;
import com.dji.dava.gateway.httpclient.GatewayClient;
import com.dji.dava.gateway.httpclient.Response;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.choerodon.core.convertor.ApplicationContextHelper;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.hzero.boot.message.entity.MessageSender;
import org.hzero.boot.message.entity.Receiver;
import org.hzero.core.base.BaseConstants;
import org.hzero.core.message.MessageAccessor;
import org.hzero.message.api.dto.UserMessageDTO;
import org.hzero.message.app.service.*;
import org.hzero.message.config.MessageConfig;
import org.hzero.message.config.MessageConfigProperties;
import org.hzero.message.constant.SpfmConstants;
import org.hzero.message.domain.entity.*;
import org.hzero.message.domain.repository.MessageReceiverRepository;
import org.hzero.message.domain.repository.MessageRepository;
import org.hzero.message.domain.repository.MessageTemplateRepository;
import org.hzero.message.domain.repository.MessageTransactionRepository;
import org.hzero.message.domain.service.IMessageLangService;
import org.hzero.message.domain.vo.Parameters;
import org.hzero.message.domain.vo.SmsRequestVO;
import org.hzero.message.infra.exception.SendMessageException;
import org.hzero.starter.sms.entity.SmsConfig;
import org.hzero.starter.sms.entity.SmsMessage;
import org.hzero.starter.sms.entity.SmsReceiver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: c-halk.liu
 * @Description:短信发送接入ums
 * @Date: 2020/10/16 14:18
 */
@Service
public class SmsSendServiceImpl extends AbstractSendService implements SmsSendService {
    private static final Logger logger = LoggerFactory.getLogger(SmsSendServiceImpl.class);
    private final ObjectMapper objectMapper;
    private final SmsServerService smsServerService;
    private final MessageRepository messageRepository;
    private final IMessageLangService messageLangService;
    private final MessageReceiverService messageReceiverService;
    private final MessageTemplateService messageTemplateService;
    private final MessageGeneratorService messageGeneratorService;
    private final MessageConfigProperties messageConfigProperties;
    private final MessageReceiverRepository messageReceiverRepository;
    private final MessageTransactionRepository messageTransactionRepository;

    private static final int SUCCESS_CODE = 200;

    @Autowired
    private MessageTemplateRepository messageTemplateRepository;

    @Autowired
    private MessageConfig messageConfig;

    @Autowired
    public SmsSendServiceImpl(ObjectMapper objectMapper, SmsServerService smsServerService, MessageRepository messageRepository, IMessageLangService messageLangService, MessageReceiverService messageReceiverService, MessageTemplateService messageTemplateService, MessageGeneratorService messageGeneratorService, MessageConfigProperties messageConfigProperties, MessageReceiverRepository messageReceiverRepository, MessageTransactionRepository messageTransactionRepository) {
        this.objectMapper = objectMapper;
        this.smsServerService = smsServerService;
        this.messageRepository = messageRepository;
        this.messageLangService = messageLangService;
        this.messageReceiverService = messageReceiverService;
        this.messageTemplateService = messageTemplateService;
        this.messageGeneratorService = messageGeneratorService;
        this.messageConfigProperties = messageConfigProperties;
        this.messageReceiverRepository = messageReceiverRepository;
        this.messageTransactionRepository = messageTransactionRepository;
    }

    @Transactional(rollbackFor = {Exception.class})
    @Override
    public Message sendMessage(MessageSender messageSender) {
        Message result = null;
        if (this.messageConfigProperties.isAsync()) {
            ((SmsSendService) ApplicationContextHelper.getContext().getBean(SmsSendService.class)).asyncSendMessage(messageSender);
        } else {
            List<MessageSender> senderList = this.messageLangService.getLang(messageSender);

            MessageSender sender;
            for(Iterator var4 = senderList.iterator(); var4.hasNext(); result = this.sendMessageWithLang(sender)) {
                sender = (MessageSender)var4.next();
            }
        }

        return result;
    }

    @Async("commonAsyncTaskExecutor")
    @Override
    public void asyncSendMessage(MessageSender messageSender) {
        List<MessageSender> senderList = this.messageLangService.getLang(messageSender);
        Iterator var3 = senderList.iterator();

        while(var3.hasNext()) {
            MessageSender sender = (MessageSender)var3.next();
            this.sendMessageWithLang(sender);
        }

    }

    private Message sendMessageWithLang(MessageSender messageSender) {
        Message message = this.createMessage(messageSender, "SMS");

        try {
            message = this.messageGeneratorService.generateMessage(messageSender, message);
            messageSender = this.messageReceiverService.queryReceiver(messageSender);
            if (CollectionUtils.isEmpty(messageSender.getReceiverAddressList())) {
                this.messageRepository.updateOptional(message.setSendFlag(BaseConstants.Flag.NO), new String[]{"sendFlag"});
                MessageTransaction transaction = (new MessageTransaction()).setMessageId(message.getMessageId()).setTrxStatusCode("P").setTenantId(message.getTenantId()).setTransactionMessage(MessageAccessor.getMessage("hmsg.error.no_receiver").desc());
                this.messageTransactionRepository.insertSelective(transaction);
                message.setTransactionId(transaction.getTransactionId());
                return message;
            }

            SmsServer smsServer = this.smsServerService.getSmsServer(messageSender.getTenantId(), messageSender.getServerCode());
            this.validServer(smsServer, messageSender.getTenantId(), messageSender.getServerCode());
            this.messageRepository.updateByPrimaryKeySelective(message);
            Iterator var4 = messageSender.getReceiverAddressList().iterator();

            while(var4.hasNext()) {
                Receiver receiver = (Receiver)var4.next();
                this.messageReceiverRepository.insertSelective((new MessageReceiver()).setMessageId(message.getMessageId()).setTenantId(message.getTenantId()).setReceiverAddress(receiver.getPhone()).setIdd(receiver.getIdd()));
            }

            this.sendMessage(messageSender.getReceiverAddressList(), message, smsServer, messageSender.getArgs());
            this.messageRepository.updateByPrimaryKeySelective(message.setSendFlag(BaseConstants.Flag.YES));
            MessageTransaction transaction = (new MessageTransaction()).setMessageId(message.getMessageId()).setTrxStatusCode("S").setTenantId(message.getTenantId());
            this.messageTransactionRepository.insertSelective(transaction);
            message.setTransactionId(transaction.getTransactionId());
        } catch (Exception var6) {
            logger.error("Send SMS failed [{} -> {}]", new Object[]{messageSender.getServerCode(), messageSender.getReceiverAddressList(), var6.fillInStackTrace()});
            this.failedProcess(message, var6);
            //手动回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }

        return message;
    }

    private void sendMessage(List<Receiver> receiverAddressList, Message message, SmsServer smsServer, Map<String, String> argMap) {
        Map<String, String> args = new HashMap(16);
        if (argMap != null && argMap.size() > 0) {
            List<String> argList = this.messageTemplateService.getTemplateArg(message.getTenantId(), message.getTemplateCode(), message.getLang());
            argMap.forEach((k, v) -> {
                if (argList.contains(k)) {
                    args.put(k, v);
                }

            });
        }

        if (smsServer == null) {
            throw new SendMessageException(String.format("Unsupported server type : type code = [%s], tenantId = [%d], templateCode = [%s]", smsServer.getServerTypeCode(), message.getTenantId(), message.getTemplateCode()));
        } else {
            List<SmsReceiver> smsReceiverList = new ArrayList();
            Iterator var14 = receiverAddressList.iterator();

            while(var14.hasNext()) {
                Receiver receiver = (Receiver)var14.next();
                smsReceiverList.add((new SmsReceiver()).setPhone(receiver.getPhone()).setIdd(receiver.getIdd()));
            }

            SmsConfig smsConfig = new SmsConfig();
            SmsMessage smsMessage = new SmsMessage();
            BeanUtils.copyProperties(smsServer, smsConfig);
            BeanUtils.copyProperties(message, smsMessage);
            MessageTemplate messageTemplate;
            MessageTemplate selectCondition = (new MessageTemplate()).setTenantId(message.getTenantId()).setTemplateCode(message.getTemplateCode()).setLang(message.getLang()).setEnabledFlag(BaseConstants.Flag.YES);
            messageTemplate = (MessageTemplate)this.messageTemplateRepository.selectOne(selectCondition);
            for (SmsReceiver smsReceiver:smsReceiverList){
                this.smsSend(smsReceiver, smsServer, smsMessage,messageTemplate.getExternalCode());
            }

        }
    }

    @Transactional(rollbackFor = {Exception.class})
    @Override
    public Message resendMessage(UserMessageDTO message) {
        if (CollectionUtils.isEmpty(message.getMessageReceiverList())) {
            return message;
        } else {
            try {
                SmsServer smsServer = this.smsServerService.getSmsServer(message.getTenantId(), message.getServerCode());
                this.validServer(smsServer, message.getTenantId(), message.getServerCode());
                this.sendMessage((List)message.getMessageReceiverList().stream().map((item) -> {
                    return (new Receiver()).setPhone(item.getReceiverAddress()).setIdd(item.getIdd());
                }).collect(Collectors.toList()), message, smsServer, this.buildArgs(message.getSendArgs()));
                this.successProcessUpdate(message);
            } catch (Exception var3) {
                logger.error("Send email failed [{} -> {}]", new Object[]{message.getServerCode(), message.getMessageReceiverList(), var3.fillInStackTrace()});
                this.failedProcessUpdate(message, var3);
                //手动回滚
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            }

            return message;
        }
    }

    private Map<String, String> buildArgs(String argsStr) {
        HashMap args = new HashMap(16);

        try {
            if (StringUtils.hasText(argsStr)) {
                JsonNode jsonNode = this.objectMapper.readTree(argsStr);
                if (jsonNode != null) {
                    Iterator iterator = jsonNode.fields();

                    while(iterator.hasNext()) {
                        Map.Entry<String, JsonNode> item = (Map.Entry)iterator.next();
                        args.put(item.getKey(), String.valueOf(item.getValue()));
                    }
                }
            }
        } catch (IOException var6) {
            logger.error("{}", ExceptionUtils.getStackTrace(var6));
            //手动回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }

        return args;
    }

    private void validServer(SmsServer smsServer, long tenantId, String serverCode) {
        if (smsServer == null || BaseConstants.Flag.NO.equals(smsServer.getEnabledFlag())) {
            throw new SendMessageException(String.format("Sms server not enabled : tenantId = [%d] , serverCode = [%s]", tenantId, serverCode));
        }
    }

    /**
     *
     * 短信接入ums
     * <AUTHOR>
     * @param smsReceiver 电话信息
     * @param smsServer 短信服务
     * @param message message信息
     * @param templateId dji-ums portal中的短信模板ID，注意模板必须已经授权给你的app
     */
    private void smsSend(SmsReceiver smsReceiver, SmsServer smsServer, SmsMessage message,String templateId) {
        SmsRequestVO smsRequestVO =new SmsRequestVO();
        smsRequestVO.setAppId(this.messageConfig.getAppId());
        smsRequestVO.setBizId(smsServer.getServerId().toString());
        smsRequestVO.setCountryCode(smsReceiver.getIdd());
        smsRequestVO.setPhoneNumber(smsReceiver.getPhone());
        Parameters parameters =new Parameters();
        parameters.setText(message.getContent());
        smsRequestVO.setParameters(parameters);
        smsRequestVO.setTemplateId(templateId);
        String payload = JSON.toJSONString(smsRequestVO);
        GatewayClient client = new GatewayClient(this.messageConfig.getUrl(), this.messageConfig.getGwId(), this.messageConfig.getGwSecret());
        String invokeUrl =  SpfmConstants.InvokeUrl.SMS_SEND_POST;
        Map<String, String> headers = new HashMap<String, String>();
        Response respPost = new Response();
        try {
            respPost = client.httpPost(invokeUrl, "", headers, payload,"json");
        } catch (Exception e) {
            logger.error("Short Message error message:{}", e.getMessage());
        }
        logger.info("Short Message results:{}", respPost);
        if (!Objects.equals(SUCCESS_CODE,respPost.getStatus()) ) {
            logger.error("Short Message  status {},error message {}", respPost.getStatus(), JSON.parseObject(respPost.getContent()).get("resultText"));
        }

    }
}
