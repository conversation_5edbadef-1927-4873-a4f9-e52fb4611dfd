package org.hzero.message.infra.repository.impl;

import io.choerodon.core.domain.*;
import io.choerodon.mybatis.pagehelper.*;
import io.choerodon.mybatis.pagehelper.domain.*;
import lombok.*;
import org.apache.commons.collections4.*;
import org.hzero.message.api.dto.*;
import org.hzero.message.domain.entity.*;
import org.hzero.message.domain.repository.*;
import org.hzero.message.domain.vo.*;
import org.hzero.message.infra.mapper.*;
import org.hzero.mybatis.base.impl.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 * 邮件规则配置表 资源库实现
 *
 * <AUTHOR> 2024-06-14 15:38:08
 */
@Component
@AllArgsConstructor
public class EmailRuleConfigRepositoryImpl extends BaseRepositoryImpl<EmailRuleConfig> implements EmailRuleConfigRepository {

    private final EmailRuleConfigMapper emailRuleConfigMapper;

    @Override
    public Page<EmailRuleConfigResVO> queryEmailRuleConfigListPage(PageRequest pageRequest, EmailRuleConfigQueryDTO queryDTO) {
        return PageHelper.doPageAndSort(pageRequest, () -> emailRuleConfigMapper.queryEmailRuleConfigListPage(queryDTO));
    }

    @Override
    public List<EmailRuleConfigExportDTO> export(EmailRuleConfigQueryDTO queryDTO) {
        return emailRuleConfigMapper.export(queryDTO);
    }

    @Override
    public List<EmailRuleConfigResImportVO> queryEmailRuleConfigListImport(Set<EmailRuleConfigInputDTO> setList) {
        if (CollectionUtils.isEmpty(setList)) {
            return new ArrayList<>();
        }
        return emailRuleConfigMapper.queryEmailRuleConfigListImport(setList);
    }

    @Override
    public void batchInsertByNative(List<EmailRuleConfig> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        emailRuleConfigMapper.batchInsertByNative(list);
    }

    @Override
    public void batchUpdateByNative(List<EmailRuleConfig> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        emailRuleConfigMapper.batchUpdateByNative(list);
    }

    @Override
    public List<EmailConfigTemplateInfoVO> queryEmailRuleConfigCmList(List<EmailRuleConfigCmQueryDTO> emailRuleConfigQueryDTOList) {
        return emailRuleConfigMapper.queryEmailRuleConfigCmList(emailRuleConfigQueryDTOList);
    }
}
