package org.hzero.message.infra.feign.impl;

import cn.hutool.system.UserInfo;
import org.hzero.message.infra.feign.SrmUserDetailsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR> hp
 * @date : 2020/1/19
 */
@Component
public class SrmUserDetailsServiceImpl implements SrmUserDetailsService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SrmUserDetailsServiceImpl.class);


    @Override
    public ResponseEntity<UserInfo> queryUserInfoByUserId(Long userId) {
        LOGGER.error("Failed to query userInfo {}" , userId);
        return null;
    }
}
