package org.hzero.message.app.service.impl;

import com.alibaba.fastjson.*;
import com.dji.dava.gateway.httpclient.*;
import lombok.*;
import lombok.experimental.*;
import lombok.extern.slf4j.*;
import org.hzero.boot.message.entity.*;
import org.hzero.message.app.service.*;
import org.hzero.message.config.*;
import org.hzero.message.constant.*;
import org.hzero.message.infra.mapper.*;
import org.springframework.stereotype.*;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/20
 */
@Service
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@AllArgsConstructor
@Slf4j
public class DjiSendCallServiceImpl implements DjiSendCallService {

    MessageConfig messageConfig;
    DjiUserGroupMapper djiUserGroupMapper;

    /**
     * 根据ad电话报警
     *
     * @param umsBaseMsgDTO
     * @return
     * @throws Exception
     */
    @Override
    public String voiceNotification(UMSBaseMsgDTO umsBaseMsgDTO) throws Exception {
        umsBaseMsgDTO.setAppId(messageConfig.getAppId());
        umsBaseMsgDTO.setBizId(UUID.randomUUID().toString());
        umsBaseMsgDTO.setPlayTimes(2);
        umsBaseMsgDTO.setSpeed(0);
        log.info("根据ad电话报警入参 :{}", JSON.toJSONString(umsBaseMsgDTO));
        GatewayClient client = new GatewayClient(this.messageConfig.getUrl(), this.messageConfig.getGwId(), this.messageConfig.getGwSecret());
        String invokeUrl = SpfmConstants.InvokeUrl.UMS_VOICE_NOTIFICATION_URL;
        Map<String, String> headers = new HashMap<>(16);
        Response respPost = client.httpPost(invokeUrl, "", headers, JSON.toJSONString(umsBaseMsgDTO), "json");
        String respString = JSON.toJSONString(respPost);
        log.info("根据ad电话报警voiceNotification:{}", respString);
        return respString;
    }

    @Override
    public List<Receiver> getUserGroupList(String userGroup) {
        return djiUserGroupMapper.selectUserGroupList(userGroup);
    }

}
